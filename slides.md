---
layout: cover
background: '#f8f9fa'
css: unocss
---

<style>
/* Import our custom presentation styles */
@import './pfe-presentation-styles.css';

/* Universal Slide Container Framework - Prevents Cutoff Issues */
.slide-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.slide-header {
  flex-shrink: 0;
  z-index: 10;
}

.slide-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  overflow: hidden;
}

/* Content Type Utilities */
.slide-image {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 0;
}

.slide-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  object-position: center;
}

.slide-table {
  flex: 1;
  overflow: auto;
  min-height: 0;
}

.slide-table table {
  width: 100%;
  font-size: 0.875rem;
  border-collapse: collapse;
}

.slide-grid {
  flex: 1;
  display: grid;
  gap: 1rem;
  align-content: start;
  min-height: 0;
}

.slide-text {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

/* Auto-scaling utilities */
.auto-scale {
  font-size: clamp(0.75rem, 2vw, 1rem);
}

.auto-scale-large {
  font-size: clamp(1rem, 3vw, 1.5rem);
}

/* Grid variations */
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive breakpoints */
@media (max-width: 768px) {
  .grid-4 { grid-template-columns: repeat(2, 1fr); }
  .grid-3 { grid-template-columns: repeat(2, 1fr); }
}

/* Completely hide built-in Slidev slide numbers - we use custom component */
.slidev-layout::after {
  display: none !important;
}

/* Hide any other potential slide number elements */
.slidev-page-num,
.slidev-slide-num {
  display: none !important;
}
</style>

<div style="position: absolute; top: 10px; left: 20px; right: 20px; display: flex; justify-content: space-between; align-items: flex-start;">
  <img src="/school-logo.png" alt="ESI Logo" style="height: 54px;">
  <img src="/company-logo.png" alt="DATAPROTECT Logo" style="height: 46px;">
</div>


<div style="position: absolute; top: 64px; left: 50%; transform: translateX(-50%); text-align: center;">
  <div style="color: black; font-size: 14px; font-weight: 600; line-height: 1.3;">
    End-of-Studies Project Defense<br>
    For Obtaining the State Engineer Diploma<br>
    <span style="font-size: 12px;">Specialization: Information Systems Security Engineering and Cyberdefense</span>
  </div>
</div>

<div class="text-center mb-8 mt-16">
</div>

<div class="text-center mb-8 mt-16">
  <h1 style="color: black; font-size: 28px; line-height: 1.2; font-weight: 600; margin: 0;">
    Development of a Platform for Cybersecurity Challenges:<br>
    Simulating Real-World Web Vulnerabilities
  </h1>
  <div style="color: #1a73e8; font-size: 20px; font-weight: 600; margin-top: 16px;">
    PENQUEST Platform
  </div>
  <hr class="border-blue-600 border-2 w-3/4 mx-auto mt-4">
</div>

<div class="flex justify-between mt-16 text-xs px-4">
  <div class="text-left" style="width: 30%;">
    <div class="font-bold mb-2">Supervised by:</div>
    <div class="space-y-1">
      <div>Dr. Ali EL KSIMI – ESI</div>
      <div>Dr. Imad ABDESSADKI – DATAPROTECT</div>
      <div>Mr. Ayoub HRAZEM – DATAPROTECT</div>
      <div>Mr. Darhbar Zine elabidine – DATAPROTECT</div>
    </div>
  </div>
  
  <div class="text-center" style="width: 25%;">
    <div class="font-bold mb-2">Presented by:</div>
    <div>Mr. BAJJI Ilyas</div>
  </div>
  
  <div class="text-left" style="width: 30%;">
    <div class="font-bold mb-2">Defense Committee:</div>
    <div class="space-y-1">
      <div>Dr. Rachid GOUARTI – President</div>
      <div>Dr. Ali EL KSIMI – Member</div>
      <div>Dr. Imad ABDESSADKI – Member</div>
    </div>
  </div>
</div>

<div class="absolute bottom-0 left-0 right-0 flex justify-center bg-blue-600 text-white px-6 py-2 text-xs">
  <span class="font-bold">2024-2025</span>
</div>

<!--
Hello
-->

---

## Presentation Outline

<div class="grid grid-cols-3 gap-6 mt-8">
  <div class="bg-gradient-to-br from-red-500 to-red-600 text-white p-5 rounded-xl relative">
    <div class="absolute top-1 right-3 bg-white bg-opacity-20 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">1</div>
    <h3 class="text-base font-semibold mb-2">Project Context & Global Challenge</h3>
  </div>

  <div class="bg-gradient-to-br from-purple-500 to-purple-600 text-white p-5 rounded-xl relative">
    <div class="absolute top-1 right-3 bg-white bg-opacity-20 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">2</div>
    <h3 class="text-base font-semibold mb-2">Theoretical Foundation & Technology</h3>
  </div>

  <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-5 rounded-xl relative">
    <div class="absolute top-2 right-3 bg-white bg-opacity-20 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">3</div>
    <h3 class="text-base font-semibold mb-2">PENQUEST Design & Architecture</h3>
  </div>

  <div class="bg-gradient-to-br from-green-500 to-green-600 text-white p-5 rounded-xl relative">
    <div class="absolute top-2 right-3 bg-white bg-opacity-20 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">4</div>
    <h3 class="text-base font-semibold mb-2">Implementation & Technical Realization</h3>
  </div>

  <div class="bg-gradient-to-br from-orange-500 to-orange-600 text-white p-5 rounded-xl relative">
    <div class="absolute top-2 right-3 bg-white bg-opacity-20 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">5</div>
    <h3 class="text-base font-semibold mb-2">Results & Educational Impact</h3>
  </div>

  <div class="bg-gradient-to-br from-indigo-500 to-indigo-600 text-white p-5 rounded-xl relative">
    <div class="absolute top-2 right-3 bg-white bg-opacity-20 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">6</div>
    <h3 class="text-base font-semibold mb-2">Conclusion & Future Perspectives</h3>
  </div>
</div>

<!--
My presentation is structured into six comprehensive sections. We'll begin by understanding the context and global cybersecurity challenge, then explore the theoretical foundations and technology choices. We'll dive into the design and architecture, examine the technical implementation, analyze the results, and conclude with future perspectives. This progression will take you from problem identification to complete solution delivery
-->

---
layout: section
background: 'linear-gradient(135deg, #ef4444, #dc2626)'
---

<div class="section-header text-center">
  <h1 class="text-8xl font-bold text-white mb-2">Section 1</h1>
  <h2 class="text-3xl font-semibold text-white">Project Context & Global Challenge</h2>
</div>

<div class="progress-nav">
  <div class="nav-item active">1. Context</div>
  <div class="nav-item">2. Foundation</div>
  <div class="nav-item">3. Design</div>
  <div class="nav-item">4. Implementation</div>
  <div class="nav-item">5. Results</div>
  <div class="nav-item">6. Conclusion</div>
</div>

---

### SECTION 1: PROJECT CONTEXT & GLOBAL CHALLENGE

<div style="text-align: center; margin: 24px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Presentation of the Organization</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">General Context</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Problem Statement</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Objectives of the Project</span>
</div>

<div class="text-center">
  <img src="/figures/company/dataprotect-logo.png" alt="DATAPROTECT Logo" class="h-20 mx-auto mb-6">
  <h4 class="text-3xl font-bold text-blue-600 mb-8">Morocco's Leading Cybersecurity Company</h4>
  <p class="text-lg text-gray-600 mb-8">"Security is our commitment"</p>
</div>

<div class="grid grid-cols-4 gap-12 text-center">
  <div>
    <div class="text-5xl font-bold text-blue-600 mb-3">220+</div>
    <div class="text-lg text-gray-600">Professionals</div>
  </div>
  <div>
    <div class="text-5xl font-bold text-green-600 mb-3">580+</div>
    <div class="text-lg text-gray-600">Certifications</div>
  </div>
  <div>
    <div class="text-5xl font-bold text-purple-600 mb-3">450+</div>
    <div class="text-lg text-gray-600">Clients</div>
  </div>
  <div>
    <div class="text-5xl font-bold text-orange-600 mb-3">40+</div>
    <div class="text-lg text-gray-600">Countries</div>
  </div>
</div>

---

### SECTION 1: PROJECT CONTEXT & GLOBAL CHALLENGE

<div style="text-align: center; margin: 24px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Presentation of the Organization</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">General Context</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Problem Statement</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Objectives of the Project</span>
</div>

<div class="flex justify-center items-center w-full h-full overflow-hidden -mt-8">
  <img src="/figures/company/dataprotect-business-units.png" alt="DATAPROTECT Business Units" class="max-w-full max-h-[45vh] object-scale-down rounded shadow">
</div>

<!--
DP HAS MANY bu to satisfy full spectrum of cybersec like
-->

---

### SECTION 1: PROJECT CONTEXT & GLOBAL CHALLENGE

<div style="text-align: center; margin: 24px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Presentation of the Organization</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">General Context</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Problem Statement</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Objectives of the Project</span>
</div>

<style>
.offensive-security-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 55vh;
  position: relative;
  padding: 2rem;
}

.business-unit-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  color: white;
  padding: 2rem 3rem;
  border-radius: 20px;
  font-size: 1.5rem;
  font-weight: bold;
  text-align: center;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
  z-index: 10;
  min-width: 300px;
}

.service-item {
  position: absolute;
  display: flex;
  align-items: center;
  background: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  font-weight: 600;
  min-width: 180px;
  z-index: 5;
}

.service-arrow {
  position: absolute;
  z-index: 1;
}

.service-arrow::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

/* Application Security - Top */
.app-security {
  top: 8%;
  left: 50%;
  transform: translateX(-50%);
  border-left-color: #ef4444;
  color: #ef4444;
}

.app-security-arrow {
  top: 25%;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 60px;
  background: #ef4444;
}

.app-security-arrow::before {
  bottom: -8px;
  left: -4px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 8px solid #ef4444;
}

/* Red Teaming - Top Right */
.red-teaming {
  top: 20%;
  right: 15%;
  border-left-color: #f97316;
  color: #f97316;
}

.red-teaming-arrow {
  top: 32%;
  right: 28%;
  width: 70px;
  height: 2px;
  background: #f97316;
  transform: rotate(-30deg);
}

.red-teaming-arrow::before {
  right: -8px;
  top: -4px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 8px solid #f97316;
}

/* Penetration Testing - Bottom Right */
.pen-testing {
  bottom: 20%;
  right: 15%;
  border-left-color: #eab308;
  color: #eab308;
}

.pen-testing-arrow {
  bottom: 32%;
  right: 28%;
  width: 70px;
  height: 2px;
  background: #eab308;
  transform: rotate(30deg);
}

.pen-testing-arrow::before {
  right: -8px;
  top: -4px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 8px solid #eab308;
}

/* DevSecOps - Bottom */
.devsecops {
  bottom: 8%;
  left: 50%;
  transform: translateX(-50%);
  border-left-color: #22c55e;
  color: #22c55e;
}

.devsecops-arrow {
  bottom: 25%;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 60px;
  background: #22c55e;
}

.devsecops-arrow::before {
  top: -8px;
  left: -4px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 8px solid #22c55e;
}

/* ICS Security - Left */
.ics-security {
  top: 50%;
  left: 15%;
  transform: translateY(-50%);
  border-left-color: #8b5cf6;
  color: #8b5cf6;
}

.ics-security-arrow {
  top: 50%;
  left: 28%;
  transform: translateY(-50%);
  width: 70px;
  height: 2px;
  background: #8b5cf6;
}

.ics-security-arrow::before {
  right: -8px;
  top: -4px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 8px solid #8b5cf6;
}

.service-icon {
  font-size: 1.5rem;
  margin-right: 0.5rem;
}
</style>

<div class="offensive-security-container">
  <!-- Central Business Unit -->
  <div class="business-unit-center">
    Security Offensive<br>
    <span style="font-size: 1.2rem; font-weight: 500;">Business Unit</span>
  </div>
  
  <!-- Application Security -->
  <div class="service-item app-security">
    <span class="service-icon">🛡️</span>
    Application Security
  </div>
  <div class="service-arrow app-security-arrow"></div>
  
  <!-- Red Teaming -->
  <div class="service-item red-teaming">
    <span class="service-icon">🎯</span>
    Red Teaming
  </div>
  <div class="service-arrow red-teaming-arrow"></div>
  
  <!-- Penetration Testing -->
  <div class="service-item pen-testing">
    <span class="service-icon">🔍</span>
    Penetration Testing
  </div>
  <div class="service-arrow pen-testing-arrow"></div>
  
  <!-- DevSecOps -->
  <div class="service-item devsecops">
    <span class="service-icon">⚙️</span>
    DevSecOps
  </div>
  <div class="service-arrow devsecops-arrow"></div>
  
  <!-- ICS Security -->
  <div class="service-item ics-security">
    <span class="service-icon">🏭</span>
    ICS Security
  </div>
  <div class="service-arrow ics-security-arrow"></div>
</div>

<!--
The Security Offensive Business Unit represents DATAPROTECT's proactive cybersecurity services, encompassing application security assessments, red team exercises that simulate real-world attacks, comprehensive penetration testing across all infrastructure layers, DevSecOps integration for secure development practices, and specialized Industrial Control Systems security for critical infrastructure protection.
-->

---

### SECTION 1: PROJECT CONTEXT & GLOBAL CHALLENGE

<div style="text-align: center; margin: 24px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Presentation of the Organization</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">General Context</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Problem Statement</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Objectives of the Project</span>
</div>

<div class="flex justify-center items-center w-full h-full overflow-hidden -mt-8">
  <img src="/figures/company/dataprotect-clients.png" alt="DATAPROTECT Clients" class="max-w-full max-h-[45vh] object-scale-down rounded shadow">
</div>

<!--
he company's impressive client portfolio spans critical sectors - banking, telecommunications, government, and industry. This real-world exposure to cybersecurity challenges across diverse environments directly informed the requirements and design decisions for the PENQUEST platform
-->

---

### SECTION 1: PROJECT CONTEXT & GLOBAL CHALLENGE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Presentation of the Organization</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">General Context</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Problem Statement</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Objectives of the Project</span>
</div>

<div class="grid grid-cols-2 gap-12 mb-8">
  <div class="text-center">
    <div class="text-6xl font-bold text-red-600 mb-4">4.8M</div>
    <div class="text-xl text-gray-700 font-semibold">Workforce Gap</div>
    <div class="text-lg text-red-500 font-bold mt-2">↑ 19% from 2023</div>
  </div>
  <div class="text-center">
    <div class="text-6xl font-bold text-blue-600 mb-4">10.2M</div>
    <div class="text-xl text-gray-700 font-semibold">Total Global Need</div>
    <div class="text-lg text-blue-500 font-bold mt-2">Professionals Required</div>
  </div>
</div>

<div class="text-center mb-4">
  <div class="text-xs text-gray-500 italic">Source: (ISC)² 2024 Cybersecurity Workforce Study</div>
</div>

<div class="flex justify-center items-center h-48 mb-6">
  <img src="/figures/company/dataprotect-clients.png" alt="DATAPROTECT Client Portfolio" class="max-w-full max-h-full object-contain rounded shadow">
</div>

<div class="text-center">
  <div class="bg-green-100 p-6 rounded-xl">
  <p class="text-2xl font-bold text-green-800">
  CTF Training: Bridge Theory-Practice Gap
  </p>
  </div>
</div>

<!--
DATAPROTECT's client diversity proves the need for cybersecurity talent across all sectors
-->

---

### SECTION 1: PROJECT CONTEXT & GLOBAL CHALLENGE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Presentation of the Organization</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">General Context</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Problem Statement</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Objectives of the Project</span>
</div>

<div class="grid grid-cols-2 gap-16">
  <div class="text-center">
    <div class="text-8xl mb-6">❌</div>
    <h4 class="text-3xl font-bold text-red-600 mb-6">Traditional Education</h4>
    <div class="text-xl text-gray-700 space-y-3">
      <div>• Theory-focused learning</div>
      <div>• Limited hands-on practice</div>
      <div>• No real-world scenarios</div>
      <div>• Static assessments</div>
    </div>
  </div>
  
  <div class="text-center">
    <div class="text-8xl mb-6">✅</div>
    <h4 class="text-3xl font-bold text-green-600 mb-6">PENQUEST Solution</h4>
    <div class="text-xl text-gray-700 space-y-3">
      <div>• Interactive CTF challenges</div>
      <div>• Containerized environments</div>
      <div>• Real vulnerability simulation</div>
      <div>• Progress tracking system</div>
    </div>
  </div>
</div>

<!--
from that an issue arises; how to train competent security professionas
-->

---

### SECTION 1: PROJECT CONTEXT & GLOBAL CHALLENGE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Presentation of the Organization</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">General Context</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Problem Statement</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Objectives of the Project</span>
</div>



<div class="grid grid-cols-4 gap-8 text-center mt-32">
  <div>
    <div class="text-6xl mb-4">⚙️</div>
    <h5 class="font-bold text-blue-600 text-lg">Technical Enhancement</h5>
  </div>
  
  <div>
    <div class="text-6xl mb-4">🎓</div>
    <h5 class="font-bold text-green-600 text-lg">Educational Innovation</h5>
  </div>
  
  <div>
    <div class="text-6xl mb-4">🎩</div>
    <h5 class="font-bold text-purple-600 text-lg">Strategic Impact</h5>
  </div>
  
  <div>
    <div class="text-6xl mb-4">📊</div>
    <h5 class="font-bold text-orange-600 text-lg">Monitoring & Quality</h5>
  </div>
</div>

<!--
the obejct is to build a platform that enables
-->

---

### SECTION 1: PROJECT CONTEXT & GLOBAL CHALLENGE

<div style="text-align: center; margin: 24px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Presentation of the Organization</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">General Context</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Problem Statement</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Objectives of the Project</span>
</div>

<div class="text-center mt-4">
  <div class="text-sm font-semibold text-blue-800 bg-blue-100 px-3 py-1 rounded inline-block mb-6">
    February - June 2025
  </div>
</div>

<img src="/figures/diagrams-gant-and-uml/diagram-gant.jpg" alt="PENQUEST Development Timeline" class="mx-auto max-w-full max-h-[50vh] object-contain rounded shadow">

<!--
project was between feb and june and started with liter in order to desin the platform and choose technolgies that will usezd later for devleopment process
-->

---
layout: section
background: 'linear-gradient(135deg, #a855f7, #9333ea)'
---

<div class="section-header text-center">
  <h1 class="text-8xl font-bold text-white mb-4">Section 2</h1>
  <h2 class="text-3xl font-semibold text-white">Theoretical Foundation & Technology Analysis</h2>
</div>

<div class="progress-nav">
  <div class="nav-item completed">1. Context</div>
  <div class="nav-item active">2. Foundation</div>
  <div class="nav-item">3. Design</div>
  <div class="nav-item">4. Implementation</div>
  <div class="nav-item">5. Results</div>
  <div class="nav-item">6. Conclusion</div>
</div>

---

### SECTION 2: THEORETICAL FOUNDATION & TECHNOLOGY ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Platform Selection</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Containerization</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Security Standards</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Technology Stack</span>
</div>

<div class="text-center mb-6">
  <h4 class="text-2xl font-bold text-purple-600">CTF Platform Benchmarking</h4>
</div>

<Transform :scale="0.9">
<div class="max-h-[60vh] overflow-auto">
  <table class="mx-auto text-sm">
    <thead>
      <tr class="bg-purple-100">
        <th class="px-3 py-2 font-bold text-purple-800">Platform</th>
        <th class="px-3 py-2 font-bold text-purple-800">CTFd</th>
        <th class="px-3 py-2 font-bold text-purple-800">FBCTF</th>
        <th class="px-3 py-2 font-bold text-purple-800">RootTheBox</th>
      </tr>
    </thead>
    <tbody>
      <tr class="border-b">
        <td class="px-3 py-2 font-semibold">Deployment</td>
        <td class="px-3 py-2">Easy</td>
        <td class="px-3 py-2">Medium</td>
        <td class="px-3 py-2">Medium</td>
      </tr>
      <tr class="border-b bg-gray-50">
        <td class="px-3 py-2 font-semibold">Assessment</td>
        <td class="px-3 py-2">Comprehensive</td>
        <td class="px-3 py-2">Limited</td>
        <td class="px-3 py-2">Advanced</td>
      </tr>
      <tr class="border-b">
        <td class="px-3 py-2 font-semibold">Plugin Support</td>
        <td class="px-3 py-2">Extensive</td>
        <td class="px-3 py-2">Minimal</td>
        <td class="px-3 py-2">Moderate</td>
      </tr>
      <tr class="bg-yellow-100">
        <td class="px-3 py-2 font-bold">Overall Rating</td>
        <td class="px-3 py-2 font-bold text-green-600">🏆 9.2/10</td>
        <td class="px-3 py-2">6.8/10</td>
        <td class="px-3 py-2">7.5/10</td>
      </tr>
    </tbody>
  </table>
</div>
</Transform>

<div class="text-center mt-6">
  <div class="bg-green-100 p-2 rounded-lg inline-block">
    <p class="text-lg font-bold text-green-800">CTFd Selected for Superior Educational Features</p>
  </div>
</div>

<div class="text-center mt-2">
  <div class="text-xs text-gray-500 italic">Source: Comparative analysis based on Karagiannis et al. (2020) evaluation methodology</div>
</div>

<!--
ctfd is slected as framework for our platform
-->

---

### SECTION 2: THEORETICAL FOUNDATION & TECHNOLOGY ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Platform Selection</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Containerization</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Security Standards</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Technology Stack</span>
</div>

<div class="text-center mb-6">
  <h4 class="text-2xl font-bold text-purple-600">Flask Framework Foundation</h4>
</div>

<div class="grid grid-cols-2 gap-8 max-h-[60vh] overflow-hidden">
  <div class="bg-blue-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-blue-700 mb-4">Plugin Architecture</h5>
    <ul class="text-gray-700 space-y-1 text-sm">
      <li>Application Context Integration</li>
      <li>Blueprint Registration</li>
      <li>Request Lifecycle Hooks</li>
      <li>Jinja2 Templating</li>
    </ul>
  </div>
  
  <div class="bg-green-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-green-700 mb-4">Key Benefits</h5>
    <ul class="text-gray-700 space-y-2 text-sm">
      <li>WSGI Standard Compliance</li>
      <li>Modular Development</li>
      <li>Educational Customization</li>
      <li>Plugin Extensibility</li>
    </ul>
  </div>
</div>

<div class="text-center mt-6">
  <div class="bg-purple-100 p-4 rounded-lg inline-block">
    <p class="text-lg font-bold text-purple-800">Foundation for CTFd's Educational Plugin System</p>
  </div>
</div>

<!--
ctfd uses flask and plugins to extend its functionslities
flask is  a python web framwork while
the plugins are exenstions that  folow standr archite that is based on :
1. Blueprint Registration
Think of blueprints as modular sections of a website:
2. Application Context Integration
This is how your plugin accesses CTFd's resources:
3. Jinja2 Templating
Jinja generates the HTML pages users see:
4. Request Lifecycle Hooks
These let you intercept requests at different stages:
-->

---

### SECTION 2: THEORETICAL FOUNDATION & TECHNOLOGY ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Platform Selection</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Containerization</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Security Standards</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Technology Stack</span>
</div>

<div class="text-center mb-6">
  <h4 class="text-2xl font-bold text-purple-600">Docker Security Fundamentals</h4>
</div>

<div class="grid grid-cols-3 gap-6 max-h-[45vh] overflow-hidden">
  <div class="bg-red-50 p-6 rounded-lg text-center">
    <h5 class="text-lg font-bold text-red-700 mb-3">Process Isolation</h5>
    <p class="text-gray-700 text-sm">Linux namespaces & cgroups ensure secure separation</p>
  </div>
  
  <div class="bg-blue-50 p-6 rounded-lg text-center">
    <h5 class="text-lg font-bold text-blue-700 mb-3">Resource Management</h5>
    <p class="text-gray-700 text-sm">Prevent resource exhaustion attacks</p>
  </div>
  
  <div class="bg-green-50 p-6 rounded-lg text-center">
    <h5 class="text-lg font-bold text-green-700 mb-3">Consistency</h5>
    <p class="text-gray-700 text-sm">Identical environments across infrastructure</p>
  </div>
</div>

<div class="text-center mt-6">
  <div class="bg-yellow-100 p-4 rounded-lg inline-block">
    <p class="text-lg font-bold text-yellow-800">Dynamic Challenge Deployment & User Isolation</p>
  </div>
</div>

<!--
docker is containirzation tech that suports:
-->

---

### SECTION 2: THEORETICAL FOUNDATION & TECHNOLOGY ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Platform Selection</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Containerization</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Security Standards</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Technology Stack</span>
</div>

<div class="text-center mb-6">
  <h4 class="text-2xl font-bold text-purple-600">Docker Swarm Orchestration</h4>
</div>

<div class="grid grid-cols-2 gap-8 max-h-[60vh] overflow-hidden">
  <div class="bg-blue-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-blue-700 mb-4">Architecture Components</h5>
    <ul class="text-gray-700 space-y-3 text-sm">
      <li>Manager Nodes: Raft consensus orchestration</li>
      <li>Worker Nodes: Task execution</li>
      <li>Services: Desired state definition</li>
      <li>Networks: Overlay secure communication</li>
    </ul>
  </div>
  
  <div class="bg-green-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-green-700 mb-4">Key Advantages</h5>
    <ul class="text-gray-700 space-y-3 text-sm">
      <li>Low Setup Complexity</li>
      <li>Minimal Learning Curve</li>
      <li>Reduced Resource Overhead</li>
      <li>Small-Medium Scale Suitability</li>
    </ul>
  </div>
</div>

<div class="text-center mt-6">
  <div class="bg-indigo-100 p-4 rounded-lg inline-block">
    <p class="text-lg font-bold text-indigo-800">Native Docker Integration for Educational Platforms</p>
  </div>
</div>

<!--
docker sarm is docker native orchestror which composed of:

this helps t reduce resorce verjaed,coplexity and impropve scalbility
-->

---

### SECTION 2: THEORETICAL FOUNDATION & TECHNOLOGY ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Platform Selection</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Containerization</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Security Standards</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Technology Stack</span>
</div>

<div class="text-center mb-6">
  <h4 class="text-2xl font-bold text-purple-600">CTFd-whale Plugin & FRP Integration</h4>
</div>

<div class="grid grid-cols-2 gap-8 max-h-[60vh] overflow-hidden">
  <div class="bg-orange-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-orange-700 mb-4">CTFd-whale Features</h5>
    <ul class="text-gray-700 space-y-3 text-sm">
      <li>Dynamic Instance Deployment</li>
      <li>User Isolation</li>
      <li>Docker Swarm Integration</li>
      <li>Administrative Management</li>
    </ul>
  </div>
  
  <div class="bg-teal-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-teal-700 mb-4">FRP Integration</h5>
    <ul class="text-gray-700 space-y-3 text-sm">
      <li>Secure Tunneling (frps/frpc)</li>
      <li>HTTP/HTTPS/TCP/UDP Support</li>
      <li>Dynamic Configuration</li>
      <li>Subdomain Isolation</li>
    </ul>
  </div>
</div>

<div class="text-center mt-6">
  <div class="bg-purple-100 p-4 rounded-lg inline-block">
    <p class="text-lg font-bold text-purple-800">Per-User Containerized Challenge Environments</p>
  </div>
</div>

<!--
ctfd also have a prebuilt plugin called ctfd whale with useds frp to expose services to intenet;

ctfd whale provides :

while frp ensures
-->

---

### SECTION 2: THEORETICAL FOUNDATION & TECHNOLOGY ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Platform Selection</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Containerization</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Security Standards</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Technology Stack</span>
</div>

<div class="text-center mb-6">
  <h4 class="text-2xl font-bold text-purple-600">OWASP Top 10 Web Application Security Risks</h4>
</div>

<div style="transform: scale(0.7); transform-origin: top center;">
<div class="grid grid-cols-2 gap-4">
  <!-- Left Column: A01-A05 -->
  <div class="space-y-2">
    <div class="bg-red-50 p-2 rounded border-l-4 border-red-500">
      <h6 class="font-bold text-red-700 text-2xl">A01: Broken Access Control</h6>
      <p class="text-base text-gray-600">Unauthorized access to resources and data</p>
    </div>
    <div class="bg-orange-50 p-2 rounded border-l-4 border-orange-500">
      <h6 class="font-bold text-orange-700 text-2xl">A02: Cryptographic Failures</h6>
      <p class="text-base text-gray-600">Weak encryption and data exposure</p>
    </div>
    <div class="bg-yellow-50 p-2 rounded border-l-4 border-yellow-500">
      <h6 class="font-bold text-yellow-700 text-2xl">A03: Injection</h6>
      <p class="text-base text-gray-600">SQL, XSS, Command injection attacks</p>
    </div>
    <div class="bg-green-50 p-2 rounded border-l-4 border-green-500">
      <h6 class="font-bold text-green-700 text-2xl">A04: Insecure Design</h6>
      <p class="text-base text-gray-600">Missing or ineffective security controls</p>
    </div>
    <div class="bg-blue-50 p-2 rounded border-l-4 border-blue-500">
      <h6 class="font-bold text-blue-700 text-2xl">A05: Security Misconfiguration</h6>
      <p class="text-base text-gray-600">Default, incomplete, or insecure configurations</p>
    </div>
  </div>
  
  <!-- Right Column: A06-A10 -->
  <div class="space-y-2">
    <div class="bg-indigo-50 p-2 rounded border-l-4 border-indigo-500">
      <h6 class="font-bold text-indigo-700 text-2xl">A06: Vulnerable Components</h6>
      <p class="text-base text-gray-600">Using components with known vulnerabilities</p>
    </div>
    <div class="bg-purple-50 p-2 rounded border-l-4 border-purple-500">
      <h6 class="font-bold text-purple-700 text-2xl">A07: Authentication Failures</h6>
      <p class="text-base text-gray-600">Weak authentication and session management</p>
    </div>
    <div class="bg-pink-50 p-2 rounded border-l-4 border-pink-500">
      <h6 class="font-bold text-pink-700 text-2xl">A08: Data Integrity Failures</h6>
      <p class="text-base text-gray-600">Untrusted software updates and data integrity issues</p>
    </div>
    <div class="bg-gray-50 p-2 rounded border-l-4 border-gray-500">
      <h6 class="font-bold text-gray-700 text-2xl">A09: Logging & Monitoring Failures</h6>
      <p class="text-base text-gray-600">Insufficient monitoring and incident response</p>
    </div>
    <div class="bg-teal-50 p-2 rounded border-l-4 border-teal-500">
      <h6 class="font-bold text-teal-700 text-2xl">A10: Server-Side Request Forgery</h6>
      <p class="text-base text-gray-600">Unvalidated URL fetching by web applications</p>
    </div>
  </div>
</div>
</div>

<div class="text-center mt-6">
  <div class="bg-green-100 p-3 rounded-lg inline-block">
    <p class="text-base font-bold text-green-800">Industry-Standard Web Application Security Framework</p>
  </div>
</div>

<!--
lets tart with owaps
owasp open woldwide app sec project
which reperst 10 catego of vul realted to web ...
-->

---

### SECTION 2: THEORETICAL FOUNDATION & TECHNOLOGY ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Platform Selection</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Containerization</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Security Standards</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Technology Stack</span>
</div>

<div class="text-center mb-6">
  <h4 class="text-2xl font-bold text-purple-600">MITRE ATT&CK Framework</h4>
</div>

<Transform :scale="0.9">
<div class="grid grid-cols-3 gap-6 max-h-[55vh] overflow-hidden">
  <div class="bg-red-50 p-4 rounded-lg">
    <h5 class="text-lg font-bold text-red-700 mb-3 text-center">Tactics</h5>
    <div class="space-y-2 text-sm">
      <div class="bg-white p-2 rounded">Initial Access</div>
      <div class="bg-white p-2 rounded">Execution</div>
      <div class="bg-white p-2 rounded">Persistence</div>
      <div class="bg-white p-2 rounded">Privilege Escalation</div>
      <div class="bg-white p-2 rounded">Defense Evasion</div>
    </div>
  </div>
  
  <div class="bg-blue-50 p-4 rounded-lg">
    <h5 class="text-lg font-bold text-blue-700 mb-3 text-center">Techniques</h5>
    <div class="space-y-2 text-sm">
      <div class="bg-white p-2 rounded">Command & Scripting</div>
      <div class="bg-white p-2 rounded">File Discovery</div>
      <div class="bg-white p-2 rounded">Web Shell</div>
      <div class="bg-white p-2 rounded">Public Exploit</div>
      <div class="bg-white p-2 rounded">Process Injection</div>
    </div>
  </div>
  
  <div class="bg-green-50 p-4 rounded-lg">
    <h5 class="text-lg font-bold text-green-700 mb-3 text-center">Procedures</h5>
    <div class="space-y-2 text-sm">
      <div class="bg-white p-2 rounded">Web Download</div>
      <div class="bg-white p-2 rounded">Code Injection</div>
      <div class="bg-white p-2 rounded">Auto-Start</div>
      <div class="bg-white p-2 rounded">Port Scan</div>
      <div class="bg-white p-2 rounded">Data Theft</div>
    </div>
  </div>
</div>
</Transform>

<!--
stands for adversary tactic and technics and common knwoledge and is framework that classifies attacks based on rela world scenarios.

it structures the approach to attack simulation through tactics, techniques, and procedures
-->

---

### SECTION 2: THEORETICAL FOUNDATION & TECHNOLOGY ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Platform Selection</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Containerization</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Security Standards</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Technology Stack</span>
</div>

<div class="text-center mb-6">
  <h4 class="text-2xl font-bold text-purple-600">CWE & CVSS Classification Systems</h4>
</div>

<Transform :scale="0.9">
<div class="grid grid-cols-2 gap-8 max-h-[60vh] overflow-hidden">
  <div class="bg-orange-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-orange-700 mb-4">CWE Taxonomy</h5>
    <div class="space-y-3 text-sm">
      <div class="bg-white p-3 rounded border border-orange-200">
        <div class="font-semibold text-orange-800">Class Level</div>
        <div class="text-gray-600">High-level categories</div>
      </div>
      <div class="bg-white p-3 rounded border border-orange-200">
        <div class="font-semibold text-orange-800">Base Level</div>
        <div class="text-gray-600">SQL Injection (CWE-89)</div>
      </div>
      <div class="bg-white p-3 rounded border border-orange-200">
        <div class="font-semibold text-orange-800">Variant Level</div>
        <div class="text-gray-600">Detailed subcategories</div>
      </div>
      <div class="bg-orange-100 p-3 rounded">
        <div class="font-bold text-orange-700">CWE Top 25</div>
        <div class="text-orange-600">Priority guidance</div>
      </div>
    </div>
  </div>
  
  <div class="bg-blue-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-blue-700 mb-4">CVSS v3.1 Scoring</h5>
    <div class="space-y-3 text-sm">
      <div class="bg-white p-3 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">Base Metrics</div>
        <div class="text-gray-600">Attack Vector, Complexity, Impact</div>
      </div>
      <div class="bg-white p-3 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">Temporal Metrics</div>
        <div class="text-gray-600">Exploit Code Maturity</div>
      </div>
      <div class="bg-white p-3 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">Environmental</div>
        <div class="text-gray-600">Context-specific factors</div>
      </div>
      <div class="bg-blue-100 p-3 rounded">
        <div class="font-bold text-blue-700">CVE Database</div>
        <div class="text-blue-600">Real-world examples</div>
      </div>
    </div>
  </div>
</div>
</Transform>

<div class="text-center mt-6">
  <div class="bg-yellow-100 p-4 rounded-lg inline-block">
    <p class="text-lg font-bold text-yellow-800">Systematic Vulnerability Classification & Prioritization</p>
  </div>
</div>

<!--
comon weaknes enumration which clasifies a vulnerbilty using an id like sqli is identified with cwe 89

for common vulnerability scoring system , it measures the severity of the vulnerability (so it represents how bad things could get if the vul is exploited)






basic :exploitabilty,impact
temporal : change with time (remediation)
env: CIA requirement for each
-->

---

### SECTION 2: THEORETICAL FOUNDATION & TECHNOLOGY ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Platform Selection</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Containerization</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Security Standards</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Technology Stack</span>
</div>

<div class="text-center mb-6">
  <h4 class="text-2xl font-bold text-purple-600">React Frontend Framework</h4>
</div>

<div class="grid grid-cols-2 gap-8 max-h-[60vh] overflow-hidden">
  <div class="bg-blue-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-blue-700 mb-4">Core Architecture</h5>
    <ul class="text-gray-700 space-y-3 text-sm">
      <li>Component-based UI development</li>
      <li>Virtual DOM optimization</li>
      <li>Efficient state management</li>
      <li>Reusable UI elements</li>
    </ul>
  </div>
  
  <div class="bg-green-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-green-700 mb-4">Educational Benefits</h5>
    <ul class="text-gray-700 space-y-3 text-sm">
      <li>Real-time challenge tracking</li>
      <li>Progress visualization</li>
      <li>Collaborative learning features</li>
      <li>Responsive design support</li>
    </ul>
  </div>
</div>

<div class="grid grid-cols-1 gap-4 mt-6 justify-center">
  <div class="bg-blue-100 p-4 rounded text-center mx-auto">
    <div class="font-bold text-blue-800">2024 Leader</div>
    <div class="text-sm text-blue-600">Most Popular</div>
  </div>
</div>

<!--
satrting with fronten where wil be talking abt React iwth most popular frontend famwrok

enables efficient UI development

For educational applications, this translates to real-time challenge tracking
-->

---

### SECTION 2: THEORETICAL FOUNDATION & TECHNOLOGY ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Platform Selection</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Containerization</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Security Standards</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Technology Stack</span>
</div>

<div class="text-center mb-6">
  <h4 class="text-2xl font-bold text-purple-600">Backend Framework Comparison</h4>
</div>

<Transform :scale="0.9">
<div class="max-h-[60vh] overflow-auto">
  <table class="mx-auto text-sm border-collapse">
    <thead>
      <tr class="bg-purple-100">
        <th class="px-4 py-3 font-bold text-purple-800 border">Framework</th>
        <th class="px-4 py-3 font-bold text-purple-800 border">FastAPI</th>
        <th class="px-4 py-3 font-bold text-purple-800 border">Flask</th>
      </tr>
    </thead>
    <tbody>
      <tr class="border-b">
        <td class="px-4 py-3 font-semibold border">Performance</td>
        <td class="px-4 py-3 border">NodeJS/Go Level</td>
        <td class="px-4 py-3 border">Good</td>
      </tr>
      <tr class="border-b bg-gray-50">
        <td class="px-4 py-3 font-semibold border">Documentation</td>
        <td class="px-4 py-3 border">Automatic OpenAPI</td>
        <td class="px-4 py-3 border">Manual</td>
      </tr>
      <tr class="border-b">
        <td class="px-4 py-3 font-semibold border">Type Safety</td>
        <td class="px-4 py-3 border">Native Type Hints</td>
        <td class="px-4 py-3 border">Optional</td>
      </tr>
      <tr class="border-b bg-gray-50">
        <td class="px-4 py-3 font-semibold border">Validation</td>
        <td class="px-4 py-3 border">Automatic</td>
        <td class="px-4 py-3 border">Manual Setup</td>
      </tr>
      <tr class="border-b">
        <td class="px-4 py-3 font-semibold border">Use Case</td>
        <td class="px-4 py-3 border">New APIs</td>
        <td class="px-4 py-3 border">CTFd Foundation</td>
      </tr>
    </tbody>
  </table>
</div>
</Transform>

<div class="text-center mt-6">
  <div class="bg-indigo-100 p-4 rounded-lg inline-block">
    <p class="text-lg font-bold text-indigo-800">Complementary Python Web Development Stack</p>
  </div>
</div>

<!--
While CTFd uses Flask,it needs lso fastapi to conect back and front
-->

---

### SECTION 2: THEORETICAL FOUNDATION & TECHNOLOGY ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Platform Selection</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Containerization</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Security Standards</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Technology Stack</span>
</div>

<div class="text-center mb-6">
  <h4 class="text-2xl font-bold text-purple-600">Monitoring Stack Architecture</h4>
</div>

<div class="grid grid-cols-3 gap-6 max-h-[55vh] overflow-hidden">
  <div class="bg-orange-50 p-6 rounded-lg text-center">
    <h5 class="text-lg font-bold text-orange-700 mb-3">Prometheus</h5>
    <div class="text-sm text-gray-700 space-y-2">
      <div>• Metrics Collection</div>
      <div>• Time-Series Database</div>
      <div>• Pull-based Architecture</div>
      <div>• Container-native</div>
    </div>
  </div>
  
  <div class="bg-blue-50 p-6 rounded-lg text-center">
    <h5 class="text-lg font-bold text-blue-700 mb-3">Grafana</h5>
    <div class="text-sm text-gray-700 space-y-2">
      <div>• Data Visualization</div>
      <div>• Dashboard Management</div>
      <div>• Multiple Data Sources</div>
      <div>• Real-time Updates</div>
    </div>
  </div>
  
  <div class="bg-green-50 p-6 rounded-lg text-center">
    <h5 class="text-lg font-bold text-green-700 mb-3">Loki</h5>
    <div class="text-sm text-gray-700 space-y-2">
      <div>• Log Aggregation</div>
      <div>• Horizontal Scaling</div>
      <div>• LogQL Queries</div>
      <div>• Privacy Protection</div>
    </div>
  </div>
</div>

<div class="text-center mt-6">
  <div class="bg-gray-100 p-4 rounded-lg inline-block">
    <p class="text-lg font-bold text-gray-800">Comprehensive Platform Observability & Educational Privacy</p>
  </div>
</div>

<!--
mon. stack we can see it combines ..







Log aggregation means collecting log messages from multiple sources and putting them in one central place for analysis.
-->

---
layout: section
background: 'linear-gradient(135deg, #3b82f6, #2563eb)'
---

<div class="section-header text-center">
  <h1 class="text-8xl font-bold text-white mb-4">Section 3</h1>
  <h2 class="text-3xl font-semibold text-white">PENQUEST Design & Architecture</h2>
</div>

<div class="progress-nav">
  <div class="nav-item completed">1. Context</div>
  <div class="nav-item completed">2. Foundation</div>
  <div class="nav-item active">3. Design</div>
  <div class="nav-item">4. Implementation</div>
  <div class="nav-item">5. Results</div>
  <div class="nav-item">6. Conclusion</div>
</div>

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

<div class="flex justify-center items-center h-full">
  <img src="/figures/diagrams-gant-and-uml/images-related-to-try1-ctfd-to-whale/CTFd-Whale Architecture.png" alt="CTFd-Whale Architecture Framework" class="max-w-full max-h-[45vh] object-contain rounded shadow">
</div>

<div class="text-center mt-4">
  <div class="bg-blue-100 p-3 rounded-lg inline-block">
    <p class="text-lg font-bold text-blue-800">🏗️ CTFd-Whale Plugin Architecture Framework</p>
  </div>
</div>

<!--
ctfd whale is plugin for CTFd that enables dynamic container deployment for challenges

here, The CTFd-Whale plugin architectur demonstrates a modular extension approach, enabling sophisticated functionality without modifying CTFd's core codebase
-->

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

#### Flask Integration Mechanisms

<div class="grid grid-cols-2 gap-8 max-h-[45vh] overflow-hidden">
  <div class="bg-purple-50 p-4 rounded-lg">
    <h5 class="text-xl font-bold text-purple-700 mb-3">Application Context Integration</h5>
    <div class="space-y-2 text-sm">
      <div class="bg-white p-2 rounded border border-purple-200">
        <div class="font-semibold text-purple-800">load(app) Function</div>
        <div class="text-gray-600">Complete Flask application context access</div>
      </div>
      <div class="bg-white p-2 rounded border border-purple-200">
        <div class="font-semibold text-purple-800">Database Connections</div>
        <div class="text-gray-600">SQLAlchemy models and custom tables</div>
      </div>
      <div class="bg-white p-2 rounded border border-purple-200">
        <div class="font-semibold text-purple-800">Authentication Systems</div>
        <div class="text-gray-600">User management and authorization</div>
      </div>
    </div>
  </div>
  
  <div class="bg-green-50 p-4 rounded-lg">
    <h5 class="text-xl font-bold text-green-700 mb-3">Extension Points</h5>
    <div class="space-y-2 text-sm">
      <div class="bg-white p-2 rounded border border-green-200">
        <div class="font-semibold text-green-800">Routes & Blueprints</div>
        <div class="text-gray-600">Flask endpoints and admin interfaces</div>
      </div>
      <div class="bg-white p-2 rounded border border-green-200">
        <div class="font-semibold text-green-800">Templates</div>
        <div class="text-gray-600">Jinja2 overrides and custom UI</div>
      </div>
      <div class="bg-white p-2 rounded border border-green-200">
        <div class="font-semibold text-green-800">Challenge Types</div>
        <div class="text-gray-600">Custom validation and scoring logic</div>
      </div>
    </div>
  </div>
</div>

<div class="text-center mt-6">
  <div class="bg-blue-100 p-3 rounded-lg inline-block">
    <p class="text-lg font-bold text-blue-800"> Modular Extension Without Core Modification</p>
  </div>
</div>

<!--
"Flask integration occurs through comprehensive application context access via the load() function, enabling database connections, authentication systems, and complete SQLAlchemy model integration. Extension points include Flask endpoints, blueprint registration, Jinja2 template overrides, and custom challenge type validation. This enables modular extension without any core CTFd modification."
-->

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

#### System Requirements & Capacity Constraints

<div class="grid grid-cols-2 gap-6 max-h-[40vh] overflow-hidden">
  <div class="bg-red-50 p-4 rounded-lg">
    <h5 class="text-lg font-bold text-red-700 mb-3">Resource Constraints</h5>
    <div class="space-y-2 text-sm">
      <div class="bg-white p-2 rounded border border-red-200">
        <div class="font-semibold text-red-800">Single Container Policy</div>
        <div class="text-gray-600">One container per user per type</div>
      </div>
      <div class="bg-white p-2 rounded border border-red-200">
        <div class="font-semibold text-red-800">Memory Allocation</div>
        <div class="text-gray-600">128MB challenge + 512MB desktop</div>
      </div>
      <div class="bg-white p-2 rounded border border-red-200">
        <div class="font-semibold text-red-800">Capacity Formula</div>
        <div class="text-gray-600">Max Users = (Memory - 3.2GB) / 640MB</div>
      </div>
    </div>
  </div>
  
  <div class="bg-blue-50 p-4 rounded-lg">
    <h5 class="text-lg font-bold text-blue-700 mb-3">Security & Network</h5>
    <div class="space-y-2 text-sm">
      <div class="bg-white p-2 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">Network Isolation</div>
        <div class="text-gray-600">172.1.0.0/16 FRP + 172.2.0.0/16 containers</div>
      </div>
      <div class="bg-white p-2 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">Rate Limiting</div>
        <div class="text-gray-600">Multi-tier protection via Nginx</div>
      </div>
      <div class="bg-white p-2 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">Container Timeout</div>
        <div class="text-gray-600">3600 seconds with 5 renewals max</div>
      </div>
    </div>
  </div>
</div>

<div class="text-center mt-6">
  <div class="bg-orange-100 p-3 rounded-lg inline-block">
    <p class="text-lg font-bold text-orange-800"> Educational Platform Scalability Framework</p>
  </div>
</div>

<!--
Each user gets isolated access to their challenges and web desktop via unique subdomains, while maintaining network security and resource limits
-->

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

#### Design Philosophy

<div class="grid grid-cols-3 gap-6 max-h-[35vh] overflow-hidden">
  <div class="bg-green-50 p-4 rounded-lg text-center">
    <div class="text-4xl mb-3">🚀</div>
    <h5 class="text-lg font-bold text-green-700 mb-2">Separation of Concerns</h5>
    <p class="text-gray-700 text-xs">Each plugin addresses specific functionality without interfering with core CTFd operations</p>
  </div>
  
  <div class="bg-blue-50 p-4 rounded-lg text-center">
    <div class="text-4xl mb-3">🧩</div>
    <h5 class="text-lg font-bold text-blue-700 mb-2">Extensibility through Composition</h5>
    <p class="text-gray-700 text-xs">Multiple plugins work together to create sophisticated functionality through simple components</p>
  </div>
  
  <div class="bg-purple-50 p-4 rounded-lg text-center">
    <div class="text-4xl mb-3">🎓</div>
    <h5 class="text-lg font-bold text-purple-700 mb-2">Educational Continuity</h5>
    <p class="text-gray-700 text-xs">Platform enhancements preserve and enhance the learning experience rather than complicating it</p>
  </div>
</div>

<div class="text-center mt-8">
  <div class="bg-yellow-100 p-4 rounded-lg inline-block">
    <p class="text-lg font-bold text-yellow-800">Technical Sophistication Serves Educational Objectives</p>
  </div>
</div>

<!--
our design will be based on
-->

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

#### UML Modeling Approach

<div class="grid grid-cols-2 gap-8 max-h-[50vh] overflow-hidden">
  <div class="bg-indigo-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-indigo-700 mb-4">Structural Modeling</h5>
    <div class="space-y-3 text-sm">
      <div class="bg-white p-3 rounded border border-indigo-200">
        <div class="font-semibold text-indigo-800">Class Diagrams</div>
        <div class="text-gray-600">Educational concepts to technical implementations</div>
      </div>
      <div class="bg-white p-3 rounded border border-indigo-200">
        <div class="font-semibold text-indigo-800">Component Architecture</div>
        <div class="text-gray-600">Plugin integration with CTFd framework</div>
      </div>
      <div class="bg-white p-3 rounded border border-indigo-200">
        <div class="font-semibold text-indigo-800">Database Schema</div>
        <div class="text-gray-600">Entity relationships and constraints</div>
      </div>
    </div>
  </div>
  
  <div class="bg-teal-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-teal-700 mb-4">Behavioral Modeling</h5>
    <div class="space-y-3 text-sm">
      <div class="bg-white p-3 rounded border border-teal-200">
        <div class="font-semibold text-teal-800">Sequence Diagrams</div>
        <div class="text-gray-600">Dynamic system interactions and workflows</div>
      </div>
      <div class="bg-white p-3 rounded border border-teal-200">
        <div class="font-semibold text-teal-800">Plugin Lifecycle</div>
        <div class="text-gray-600">Discovery, loading, and activation processes</div>
      </div>
      <div class="bg-white p-3 rounded border border-teal-200">
        <div class="font-semibold text-teal-800">User Workflows</div>
        <div class="text-gray-600">Container provisioning and desktop access</div>
      </div>
    </div>
  </div>
</div>

<div class="text-center mt-4">
  <div class="bg-gray-100 p-3 rounded-lg inline-block">
    <p class="text-lg font-bold text-gray-800">Systematic Progression from Requirements to Implementation</p>
  </div>
</div>

<!--
Our UML modeling approach combines structural modeling through class diagrams, component architecture, and database schema design with behavioral modeling including sequence diagrams, plugin lifecycle management, and user workflow documentation. This systematic progression ensures requirements translate effectively to implementation
-->

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

<div class="flex justify-center items-center h-full">
  <img src="/figures/diagrams-gant-and-uml/images-related-to-try2-whale-to-webdesktop/High-Level Architecture.png" alt="PENQUEST High-Level Platform Architecture" class="max-w-full max-h-[45vh] object-contain rounded shadow">
</div>

<!--
This is a System Architecture Diagram that shows how all the pieces of PENQUEST fit together and communicate with each other

ctf platform!!host chal
dashobord to visua lizelogs
chall track where we will track chall dev process
-->

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

<div class="flex justify-center items-center h-full">
  <img src="/figures/diagrams-gant-and-uml/images-related-to-try2-whale-to-webdesktop/Platform Overview.png" alt="PENQUEST Platform Component Overview" class="max-w-full max-h-[45vh] object-contain rounded shadow">
</div>

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

<div class="flex justify-center items-center h-full -mt-8">
  <img src="/figures/diagrams-gant-and-uml/images-related-to-try2-whale-to-webdesktop/class diagram.png" alt="Web Desktop Database Models Class Diagram" class="max-w-full max-h-[45vh] object-contain rounded shadow">
</div>

<div class="text-center mt-4">
  <div class="bg-blue-100 p-3 rounded-lg inline-block">
    <p class="text-lg font-bold text-blue-800">Web Desktop Component Class Structure & Database Relationships</p>
  </div>
</div>

<!--
When a user starts a challenge, the system creates two isolated containers: a DynamicDockerChallenge instance (containing the vulnerable application) and a Web Desktop environment (equipped with cybersecurity tools like Kali Linux). The user accesses their web desktop through the browser and uses the pre-installed security tools to interact with and solve the challenge container. This provides a complete, isolated cybersecurity workstation where students can practice real-world penetration testing scenarios using professional tools, all within a secure, containerized environment.
-->

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

<div class="flex justify-center items-center h-full -mt-8">
  <img src="/figures/diagrams-gant-and-uml/images-related-to-try3-security-monitor/Request Monitoring Flow.png" alt="Security Monitoring Request Flow and Event Collection" class="max-w-full max-h-[45vh] object-contain rounded shadow">
</div>

<div class="text-center mt-4">
  <div class="bg-red-100 p-3 rounded-lg inline-block">
    <p class="text-lg font-bold text-red-800">Security Event Collection & Request Lifecycle Integration</p>
  </div>
</div>

<!--
This UML sequence diagram illustrates our transparent security monitoring approach, showing how every user request flows through multiple security layers - from Nginx rate limiting to our Security Plugin's before/after request hooks, with comprehensive logging and real-time alerting through Prometheus and Alert Manager integration




1. Normal Request Flow:

User sends HTTP request
Nginx checks rate limits
If OK, forwards to CTFd App
Security Plugin monitors the request (before_request hook)
CTFd processes the request (after_request hook)
Response sent back to user

2. Rate Limit Exceeded (alt box):

Nginx detects "429 Too Many Requests"
Blocks request, sends rate limit event
User gets blocked

3. Security Monitoring (alt box):

Security Plugin logs all events
Stores security events in database
Checks alert thresholds
Creates alerts if suspicious activity detected

4. Metrics Collection:

System scrapes metrics
Returns data in Prometheus format
-->

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

<div class="flex justify-center items-center h-full -mt-8">
  <img src="/figures/diagrams-gant-and-uml/images-for-challenge-track/Database Schema.png" alt="Challenge Tracking Database Architecture" class="max-w-full max-h-[45vh] object-contain rounded shadow">
</div>

<div class="text-center mt-4">
  <div class="bg-green-100 p-3 rounded-lg inline-block">
    <p class="text-lg font-bold text-green-800">Domain-Driven Design for Educational Assessment & Framework Integration</p>
  </div>
</div>

<!--
This database schema demonstrates our systematic approach to cybersecurity education, mapping each challenge to established security frameworks (OWASP, CWE, MITRE) while maintaining comprehensive audit trails and deployment tracking for educational assessment and platform management
-->

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

<div class="flex justify-center items-center h-full -mt-8">
  <img src="/figures/diagrams-gant-and-uml/images-related-to-try2-whale-to-webdesktop/sequence diagram.png" alt="Web Desktop Extension User Interaction Sequence" class="max-w-full max-h-[45vh] object-contain rounded shadow">
</div>

<div class="text-center mt-4">
  <div class="bg-blue-100 p-3 rounded-lg inline-block">
    <p class="text-lg font-bold text-blue-800">Web Desktop User Interaction & Container Provisioning Workflow</p>
  </div>
</div>

<!--
This sequence diagram of desktop plugin that illustrates the complete user workflow for accessing containerized desktop environments: users navigate to the Web Desktop interface, select from available templates (Kali Linux, Ubuntu), which triggers the Web Desktop Plugin to coordinate with Whale Plugin and Docker API for container provisioning, and finally receive an access URL to launch their personalized web-based cybersecurity workstation directly in the browser.



This diagram illustrates the Web Desktop Extension User Interaction Sequence - the complete workflow when a user launches a web-based desktop environment.

1. Template Selection:

User navigates to Web Desktop
Web UI calls getAvailableTemplates()
System returns available desktop templates (Kali Linux, Ubuntu, etc.)
User sees template options and selects one

2. Desktop Launch Process:

User selects desktop template
Web UI calls launchDesktop(templateId)
Web Desktop Plugin creates container record in database
Plugin calls Whale Plugin: createContainer(userId, templateImage)
Whale Plugin calls Docker API: createService(dockerImage, params)
Docker API returns container information
System returns container details back through the chain
Web UI displays desktop access URL to user

3. Desktop Access:

User clicks desktop access link
Opens web-based desktop environment in browser
-->

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

<div class="flex justify-center items-center h-full -mt-8">
  <img src="/figures/diagrams-gant-and-uml/images-related-to-try3-security-monitor/Architecture.png" alt="Security Monitoring System Architecture" class="max-w-full max-h-[45vh] object-contain rounded shadow">
</div>

<div class="text-center mt-4">
  <div class="bg-red-100 p-3 rounded-lg inline-block">
    <p class="text-lg font-bold text-red-800">Transparent Shield Methodology - Comprehensive Platform Monitoring</p>
  </div>
</div>

<!--
Security Monitoring System Architecture Diagram
we see dtasources collected and procesed




1. Log Collection:

All platform components generate different types of logs
Security Monitor Plugin creates security events
Logs flow into the monitoring stack

2. Processing:

Promtail ships logs to Loki
Prometheus collects metrics
Loki aggregates all log data

3. Visualization:

Grafana queries both Prometheus and Loki
Creates dashboards and alerts
Provides real-time monitoring interface
-->

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

<div class="flex justify-center items-center h-full -mt-8">
  <img src="/figures/diagrams-gant-and-uml/images-for-challenge-track/System Architecture.png" alt="Challenge Tracking System Design" class="max-w-full max-h-[45vh] object-contain rounded shadow">
</div>

<div class="text-center mt-4">
  <div class="bg-green-100 p-3 rounded-lg inline-block">
    <p class="text-lg font-bold text-green-800">⚙️ React + FastAPI Architecture for Educational Assessment & Framework Integration</p>
  </div>
</div>

<!--
Layered Architecture Diagram

This layered architecture diagram demonstrates the Challenge Tracker's systematic design approach, with clear separation between presentation (React), business logic (FastAPI), data persistence (SQLite), and infrastructure (Docker).



The architecture integrates external security frameworks (OWASP, MITRE, CWE, CVSS) to provide comprehensive cybersecurity education metadata and assessment capabilities
-->

---

### SECTION 3: PENQUEST DESIGN & ARCHITECTURE

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Architecture Foundation</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">System Modeling</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Database Design</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Component Specialization</span>
</div>

#### Integration Patterns

<div class="grid grid-cols-3 gap-6 max-h-[55vh] overflow-hidden">
  <div class="bg-purple-50 p-6 rounded-lg text-center">
    <h5 class="text-lg font-bold text-purple-700 mb-3">Plugin Ecosystem</h5>
    <div class="text-sm text-gray-700 space-y-2">
      <div class="bg-white p-2 rounded border border-purple-200">
        <div class="font-semibold text-purple-800">CTFd Core</div>
        <div class="text-gray-600">Competition framework</div>
      </div>
      <div class="bg-white p-2 rounded border border-purple-200">
        <div class="font-semibold text-purple-800">CTFd-Whale</div>
        <div class="text-gray-600">Container orchestration</div>
      </div>
      <div class="bg-white p-2 rounded border border-purple-200">
        <div class="font-semibold text-purple-800">Web Desktop</div>
        <div class="text-gray-600">Browser-based environments</div>
      </div>
      <div class="bg-white p-2 rounded border border-purple-200">
        <div class="font-semibold text-purple-800">Security Monitor</div>
        <div class="text-gray-600">Platform observability</div>
      </div>
    </div>
  </div>
  
  <div class="bg-blue-50 p-6 rounded-lg text-center">
    <h5 class="text-lg font-bold text-blue-700 mb-3">Separation of Concerns</h5>
    <div class="text-sm text-gray-700 space-y-2">
      <div class="bg-white p-2 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">Modular Design</div>
        <div class="text-gray-600">Independent plugin development</div>
      </div>
      <div class="bg-white p-2 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">Educational Focus</div>
        <div class="text-gray-600">Learning-first approach</div>
      </div>
      <div class="bg-white p-2 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">Extensible Framework</div>
        <div class="text-gray-600">Composition over inheritance</div>
      </div>
    </div>
  </div>
  
  <div class="bg-green-50 p-6 rounded-lg text-center">
    <h5 class="text-lg font-bold text-green-700 mb-3">Educational Continuity</h5>
    <div class="text-sm text-gray-700 space-y-2">
      <div class="bg-white p-2 rounded border border-green-200">
        <div class="font-semibold text-green-800">Framework Integration</div>
        <div class="text-gray-600">OWASP, MITRE, CWE, CVSS</div>
      </div>
      <div class="bg-white p-2 rounded border border-green-200">
        <div class="font-semibold text-green-800">Progressive Assessment</div>
        <div class="text-gray-600">Learning process tracking</div>
      </div>
      <div class="bg-white p-2 rounded border border-green-200">
        <div class="font-semibold text-green-800">Authentic Environments</div>
        <div class="text-gray-600">Real-world tool access</div>
      </div>
    </div>
  </div>
</div>

<!--
now that we know what we want and its design
lets recap
the paltform should  respect the sepe


This shows how PENQUEST achieves unified functionality through three strategic integration approaches.

first
🧩 Plugin Ecosystem (Left Column):
Building Block Strategy:

Start with CTFd Core as the foundation
Add CTFd-Whale for containerization
Layer Web Desktop for tool access

second:
-->

---
layout: section
background: 'linear-gradient(135deg, #10b981, #059669)'
---

<div class="section-header text-center">
  <h1 class="text-8xl font-bold text-white mb-4">Section 4</h1>
  <h2 class="text-3xl font-semibold text-white">Implementation & Technical Realization</h2>
</div>

<div class="progress-nav">
  <div class="nav-item completed">1. Context</div>
  <div class="nav-item completed">2. Foundation</div>
  <div class="nav-item completed">3. Design</div>
  <div class="nav-item active">4. Implementation</div>
  <div class="nav-item">5. Results</div>
  <div class="nav-item">6. Conclusion</div>
</div>

---

### SECTION 4: IMPLEMENTATION & DEVELOPMENT REALIZATION

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Infrastructure</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Core Plugins</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Applications</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Integration</span>
</div>

#### Docker Swarm Setup & Network Configuration

<div class="grid grid-cols-2 gap-8 max-h-[55vh] overflow-hidden">
  <div class="bg-blue-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-blue-700 mb-4">Docker Swarm Initialization</h5>
    <div class="bg-gray-900 text-green-400 p-4 rounded text-xs font-mono">
      <div class="text-yellow-400"># Initialize Docker Swarm cluster</div>
      <div><span class="text-cyan-300">docker</span> swarm init</div>
      <div></div>
      <div class="text-yellow-400"># Label nodes for targeted deployment</div>
      <div><span class="text-cyan-300">docker</span> node update --label-add <span class="text-green-300">"name=linux-1"</span> $(docker node ls -q)</div>
      <div></div>
      <div class="text-yellow-400"># Verify swarm status</div>
      <div><span class="text-cyan-300">docker</span> info | grep Swarm</div>
    </div>
  </div>
  
  <div class="bg-green-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-green-700 mb-4">Overlay Network Creation</h5>
    <div class="bg-gray-900 text-green-400 p-4 rounded text-xs font-mono">
      <div class="text-yellow-400"># Create FRP communication network</div>
      <div><span class="text-cyan-300">docker</span> network create --driver overlay --attachable frp_connect</div>
      <div></div>
      <div class="text-yellow-400"># Create container isolation network</div>
      <div><span class="text-cyan-300">docker</span> network create --driver overlay --attachable --internal frp_containers</div>
      <div></div>
      <div class="text-yellow-400"># Network ranges: 172.1.0.0/16 & 172.2.0.0/16</div>
    </div>
  </div>
</div>

---

### SECTION 4: IMPLEMENTATION & DEVELOPMENT REALIZATION

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Infrastructure</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Core Plugins</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Applications</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Integration</span>
</div>

#### FRP Configuration & Core Services

<div class="grid grid-cols-2 gap-8 max-h-[55vh] overflow-hidden">
  <div class="bg-orange-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-orange-700 mb-4">FRP Server Configuration</h5>
    <div class="bg-gray-900 text-green-400 p-4 rounded text-xs font-mono">
      <div class="text-yellow-400"># Fast Reverse Proxy server setup</div>
      <div class="text-cyan-300">[common]</div>
      <div>bind_port = <span class="text-green-300">6490</span></div>
      <div>token = <span class="text-green-300">your_secure_token</span></div>
      <div>vhost_http_port = <span class="text-green-300">8001</span></div>
      <div></div>
      <div class="text-yellow-400"># Enables container access tunneling</div>
      <div class="text-yellow-400"># Secures communication between containers and users</div>
    </div>
  </div>
  
  <div class="bg-purple-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-purple-700 mb-4">Docker Compose Services</h5>
    <div class="bg-gray-900 text-green-400 p-4 rounded text-xs font-mono">
      <div class="text-yellow-400"># Core platform services orchestration</div>
      <div><span class="text-blue-300">services:</span></div>
      <div>&nbsp;&nbsp;<span class="text-white">ctfd:</span></div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;mem_limit: <span class="text-green-300">450M</span></div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;volumes: <span class="text-green-300">/var/run/docker.sock:/var/run/docker.sock</span></div>
      <div>&nbsp;&nbsp;<span class="text-white">db:</span></div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;image: <span class="text-green-300">mariadb:10.4.12</span></div>
      <div>&nbsp;&nbsp;<span class="text-white">redis:</span></div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;image: <span class="text-green-300">redis:4</span></div>
    </div>
  </div>
</div>

---

### SECTION 4: IMPLEMENTATION & DEVELOPMENT REALIZATION

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Infrastructure</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Core Plugins</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Applications</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Integration</span>
</div>

#### Web Desktop Plugin Implementation

<div class="grid grid-cols-2 gap-8 max-h-[55vh] overflow-hidden">
  <div class="bg-blue-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-blue-700 mb-4">Plugin Initialization</h5>
    <div class="bg-gray-900 text-green-400 p-4 rounded text-xs font-mono">
      <div class="text-yellow-400"># CTFd plugin load function</div>
      <div><span class="text-blue-300">def</span> <span class="text-white">load</span>(app):</div>
      <div>&nbsp;&nbsp;<span class="text-yellow-400"># Register plugin assets</span></div>
      <div>&nbsp;&nbsp;register_plugin_assets_directory(app, base_path=<span class="text-green-300">f"/plugins/{plugin_name}/assets"</span>)</div>
      <div>&nbsp;&nbsp;</div>
      <div>&nbsp;&nbsp;<span class="text-yellow-400"># Create database tables</span></div>
      <div>&nbsp;&nbsp;<span class="text-blue-300">with</span> app.app_context():</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;create_all()</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;db_upgrade()</div>
      <div>&nbsp;&nbsp;</div>
      <div>&nbsp;&nbsp;<span class="text-yellow-400"># Register Flask Blueprint</span></div>
      <div>&nbsp;&nbsp;app.register_blueprint(page_blueprint)</div>
    </div>
  </div>
  
  <div class="bg-green-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-green-700 mb-4">Container Management</h5>
    <div class="bg-gray-900 text-green-400 p-4 rounded text-xs font-mono">
      <div class="text-yellow-400"># Desktop container creation</div>
      <div><span class="text-blue-300">def</span> <span class="text-white">try_add_container</span>(user_id, template_id):</div>
      <div>&nbsp;&nbsp;template = DesktopTemplate.query.get(template_id)</div>
      <div>&nbsp;&nbsp;</div>
      <div>&nbsp;&nbsp;<span class="text-yellow-400"># Configure container with whale integration</span></div>
      <div>&nbsp;&nbsp;container_config = {</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;<span class="text-green-300">'docker_image'</span>: template.docker_image,</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;<span class="text-green-300">'memory_limit'</span>: template.memory_limit,</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;<span class="text-green-300">'desktop_port'</span>: template.desktop_port</div>
      <div>&nbsp;&nbsp;}</div>
      <div>&nbsp;&nbsp;</div>
      <div>&nbsp;&nbsp;<span class="text-yellow-400"># Delegate to whale's container management</span></div>
      <div>&nbsp;&nbsp;<span class="text-blue-300">return</span> WhaleControlUtil.try_add_container(**container_config)</div>
    </div>
  </div>
</div>

<div class="text-center mt-6">
  <div class="bg-blue-100 p-3 rounded-lg inline-block">
    <p class="text-lg font-bold text-blue-800">Browser-Based Cybersecurity Environment Access</p>
  </div>
</div>

---

### SECTION 4: IMPLEMENTATION & DEVELOPMENT REALIZATION

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Infrastructure</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Core Plugins</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Applications</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Integration</span>
</div>

#### Security Monitoring Plugin Implementation

<div class="grid grid-cols-2 gap-8 max-h-[55vh] overflow-hidden">
  <div class="bg-red-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-red-700 mb-4">Request Lifecycle Hooks</h5>
    <div class="bg-gray-900 text-green-400 p-4 rounded text-xs font-mono">
      <div class="text-yellow-400"># Flask before_request hook</div>
      <div><span class="text-blue-300">@app.before_request</span></div>
      <div><span class="text-blue-300">def</span> <span class="text-white">security_check</span>():</div>
      <div>&nbsp;&nbsp;ip = get_ip()  <span class="text-yellow-400"># CTFd utility</span></div>
      <div>&nbsp;&nbsp;endpoint = request.endpoint <span class="text-blue-300">or</span> request.path</div>
      <div>&nbsp;&nbsp;</div>
      <div>&nbsp;&nbsp;<span class="text-yellow-400"># Skip static resources for performance</span></div>
      <div>&nbsp;&nbsp;<span class="text-blue-300">if</span> endpoint <span class="text-blue-300">and</span> endpoint.startswith((<span class="text-green-300">'static'</span>, <span class="text-green-300">'themes'</span>)):</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;<span class="text-blue-300">return</span></div>
      <div>&nbsp;&nbsp;</div>
      <div>&nbsp;&nbsp;<span class="text-yellow-400"># Threat analysis</span></div>
      <div>&nbsp;&nbsp;app.security_monitor.process_request(endpoint, ip)</div>
    </div>
  </div>
  
  <div class="bg-orange-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-orange-700 mb-4">Real-time Threat Detection</h5>
    <div class="bg-gray-900 text-green-400 p-4 rounded text-xs font-mono">
      <div class="text-yellow-400"># Rate limiting with sliding window</div>
      <div><span class="text-blue-300">def</span> <span class="text-white">_analyze_request_patterns</span>(self, endpoint, ip):</div>
      <div>&nbsp;&nbsp;key = <span class="text-green-300">f"{ip}:{endpoint}"</span></div>
      <div>&nbsp;&nbsp;now = time.time()</div>
      <div>&nbsp;&nbsp;window_size = <span class="text-cyan-300">60</span>  <span class="text-yellow-400"># 60-second window</span></div>
      <div>&nbsp;&nbsp;</div>
      <div>&nbsp;&nbsp;<span class="text-yellow-400"># Cleanup expired entries</span></div>
      <div>&nbsp;&nbsp;self.rate_limit_cache[key] = [</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;timestamp <span class="text-blue-300">for</span> timestamp <span class="text-blue-300">in</span> self.rate_limit_cache[key]</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;<span class="text-blue-300">if</span> now - timestamp < window_size</div>
      <div>&nbsp;&nbsp;]</div>
      <div>&nbsp;&nbsp;</div>
      <div>&nbsp;&nbsp;<span class="text-yellow-400"># Check threshold & trigger alerts</span></div>
      <div>&nbsp;&nbsp;<span class="text-blue-300">if</span> len(self.rate_limit_cache[key]) > threshold:</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;self._trigger_rate_limit_alert(endpoint, ip)</div>
    </div>
  </div>
</div>

---

### SECTION 4: IMPLEMENTATION & DEVELOPMENT REALIZATION

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Infrastructure</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Core Plugins</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Applications</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Integration</span>
</div>

#### Challenge Tracking System - FastAPI + React Architecture

<div class="grid grid-cols-2 gap-8 max-h-[55vh] overflow-hidden">
  <div class="bg-green-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-green-700 mb-4">FastAPI Backend</h5>
    <div class="bg-gray-900 text-green-400 p-4 rounded text-xs font-mono">
      <div class="text-yellow-400"># Modern API with automatic validation</div>
      <div><span class="text-blue-300">@app.get</span>(<span class="text-green-300">"/api/challenges"</span>, response_model=List[ChallengeResponse])</div>
      <div><span class="text-blue-300">def</span> <span class="text-white">get_challenges</span>(</div>
      <div>&nbsp;&nbsp;challenge_type: Optional[str] = Query(None),</div>
      <div>&nbsp;&nbsp;difficulty: Optional[str] = Query(None),</div>
      <div>&nbsp;&nbsp;db: Session = Depends(get_db)</div>
      <div>):</div>
      <div>&nbsp;&nbsp;query = db.query(Challenge)</div>
      <div>&nbsp;&nbsp;</div>
      <div>&nbsp;&nbsp;<span class="text-yellow-400"># Dynamic filtering</span></div>
      <div>&nbsp;&nbsp;<span class="text-blue-300">if</span> challenge_type:</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;query = query.filter(Challenge.challenge_type == challenge_type)</div>
      <div>&nbsp;&nbsp;<span class="text-blue-300">if</span> difficulty:</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;query = query.filter(Challenge.difficulty == difficulty)</div>
      <div>&nbsp;&nbsp;</div>
      <div>&nbsp;&nbsp;<span class="text-blue-300">return</span> query.order_by(Challenge.created_at.desc()).all()</div>
    </div>
  </div>
  
  <div class="bg-purple-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-purple-700 mb-4">Security Framework Integration</h5>
    <div class="bg-gray-900 text-green-400 p-4 rounded text-xs font-mono">
      <div class="text-yellow-400"># Challenge model with comprehensive mappings</div>
      <div><span class="text-blue-300">class</span> <span class="text-white">Challenge</span>(Base):</div>
      <div>&nbsp;&nbsp;<span class="text-purple-300">__tablename__</span> = <span class="text-green-300">"challenges"</span></div>
      <div>&nbsp;&nbsp;</div>
      <div>&nbsp;&nbsp;<span class="text-yellow-400"># Security framework mappings</span></div>
      <div>&nbsp;&nbsp;owasp_category = Column(String)  <span class="text-yellow-400"># A01, A02, etc.</span></div>
      <div>&nbsp;&nbsp;primary_cwe = Column(String)     <span class="text-yellow-400"># CWE-89, etc.</span></div>
      <div>&nbsp;&nbsp;mitre_attack_techniques = Column(JSON)</div>
      <div>&nbsp;&nbsp;cvss_score = Column(Float)       <span class="text-yellow-400"># Severity</span></div>
      <div>&nbsp;&nbsp;cvss_vector = Column(String)     <span class="text-yellow-400"># Vector</span></div>
      <div>&nbsp;&nbsp;</div>
      <div>&nbsp;&nbsp;<span class="text-yellow-400"># Educational metadata</span></div>
      <div>&nbsp;&nbsp;difficulty = Column(String)      <span class="text-yellow-400"># Easy/Medium/Hard</span></div>
      <div>&nbsp;&nbsp;challenge_type = Column(String)  <span class="text-yellow-400"># Web/Crypto/Pwn</span></div>
    </div>
  </div>
</div>

---

### SECTION 4: IMPLEMENTATION & DEVELOPMENT REALIZATION

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Infrastructure</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Core Plugins</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Applications</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Integration</span>
</div>

#### Security Hardening & System Validation

<div class="grid grid-cols-2 gap-8 max-h-[55vh] overflow-hidden">
  <div class="bg-red-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-red-700 mb-4">Container Security Configuration</h5>
    <div class="bg-gray-900 text-green-400 p-4 rounded text-xs font-mono">
      <div class="text-yellow-400"># Security-hardened container deployment</div>
      <div><span class="text-blue-300">services:</span></div>
      <div>&nbsp;&nbsp;<span class="text-white">ctfd:</span></div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;security_opt:</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;- <span class="text-green-300">no-new-privileges:true</span></div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;- <span class="text-green-300">seccomp:unconfined</span></div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;cap_drop: [<span class="text-green-300">ALL</span>]</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;cap_add: [<span class="text-green-300">NET_BIND_SERVICE</span>, <span class="text-green-300">SETUID</span>, <span class="text-green-300">SETGID</span>]</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;<span class="text-yellow-400"># Resource limits</span></div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;mem_limit: <span class="text-green-300">450M</span></div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;tmpfs:</div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;- <span class="text-green-300">/tmp:noexec,nosuid,size=100m</span></div>
      <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;- <span class="text-green-300">/var/tmp:noexec,nosuid,size=50m</span></div>
    </div>
  </div>
  
  <div class="bg-blue-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-blue-700 mb-4">System Capacity Validation</h5>
    <div class="bg-gray-900 text-green-400 p-4 rounded text-xs font-mono">
      <div class="text-yellow-400"># Memory usage calculation formula</div>
      <div><span class="text-blue-300">Max Users</span> = (<span class="text-white">Total RAM</span> - <span class="text-cyan-300">3.2GB</span>) / <span class="text-cyan-300">640MB</span></div>
      <div></div>
      <div class="text-yellow-400"># Per-user container allocation</div>
      <div>Web Desktop: <span class="text-green-300">512MB</span>  <span class="text-yellow-400"># Kali environment</span></div>
      <div>Challenge:   <span class="text-green-300">128MB</span>  <span class="text-yellow-400"># Vulnerable app</span></div>
      <div>Total/User:  <span class="text-green-300">640MB</span>  <span class="text-yellow-400"># Combined</span></div>
      <div></div>
      <div class="text-yellow-400"># Platform capacity examples</div>
      <div><span class="text-cyan-300">16GB</span> RAM: <span class="text-green-300">20 users</span>   <span class="text-yellow-400"># Standard classroom</span></div>
      <div><span class="text-cyan-300">32GB</span> RAM: <span class="text-green-300">45 users</span>   <span class="text-yellow-400"># Large workshop</span></div>
      <div><span class="text-cyan-300">64GB</span> RAM: <span class="text-green-300">95 users</span>   <span class="text-yellow-400"># Competition scale</span></div>
    </div>
  </div>
</div>

<!--
we apply :
  least priv
  and resource limits



no-new-privileges:true - Prevents privilege escalation attacks
seccomp:unconfined - System call filtering for security
cap_drop: [ALL] - Removes all dangerous capabilities
cap_add: [NET_BIND_SERVICE, SETUID, SETGID] - Only adds essential capabilities
mem_limit: 450M - Prevents memory exhaustion attacks
tmpfs mounts - Secure temporary file systems with noexec,nosuid flags
-->

---
layout: section
background: 'linear-gradient(135deg, #f97316, #ea580c)'
---

<div class="section-header text-center">
  <h1 class="text-8xl font-bold text-white mb-4">Section 5</h1>
  <h2 class="text-3xl font-semibold text-white">Results & Validation Analysis</h2>
</div>

<div class="progress-nav">
  <div class="nav-item completed">1. Context</div>
  <div class="nav-item completed">2. Foundation</div>
  <div class="nav-item completed">3. Design</div>
  <div class="nav-item completed">4. Implementation</div>
  <div class="nav-item active">5. Results</div>
  <div class="nav-item">6. Conclusion</div>
</div>

---

### SECTION 5: RESULTS & VALIDATION ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Deliverables</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Demo</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Performance</span>
</div>

#### Challenge Portfolio & Platform Interfaces

<div class="grid grid-cols-2 gap-8 max-h-[55vh] overflow-hidden">
  <div class="bg-blue-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-blue-700 mb-4">Challenge Portfolio (7 Challenges)</h5>
    <div class="space-y-2 text-xs">
      <div class="bg-white p-2 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">SQLi Fashion Store</div>
        <div class="text-gray-600">SQL Injection - CVSS 7.5</div>
      </div>
      <div class="bg-white p-2 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">ProfilePic Backdoor</div>
        <div class="text-gray-600">File Upload - CVSS 8.8</div>
      </div>
      <div class="bg-white p-2 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">FileInjectX</div>
        <div class="text-gray-600">Remote File Inclusion - CVSS 9.8</div>
      </div>
      <div class="bg-white p-2 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">SSRF Two-Part Flag</div>
        <div class="text-gray-600">Server-Side Request Forgery - CVSS 7.5</div>
      </div>
    </div>
  </div>
  
  <div class="bg-green-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-green-700 mb-4">Platform Components</h5>
    <div class="space-y-3 text-sm">
      <div class="bg-white p-3 rounded border border-green-200">
        <div class="font-semibold text-green-800">CVSS Range: 6.1 - 9.8</div>
        <div class="text-gray-600">Medium to Critical severity coverage</div>
      </div>
      <div class="bg-white p-3 rounded border border-green-200">
        <div class="font-semibold text-green-800">OWASP Coverage: 5/10</div>
        <div class="text-gray-600">A01, A03, A08, A10 categories</div>
      </div>
      <div class="bg-white p-3 rounded border border-green-200">
        <div class="font-semibold text-green-800">Complete Platform Stack</div>
        <div class="text-gray-600">CTFd + Whale + Web Desktop + Security Monitor</div>
      </div>
    </div>
  </div>
</div>

<!--
now bzsisdes the paltform we have 7 challenges that are created and mapped to cvss from medium to critical and covers 5 of owasp10
-->

---

### SECTION 5: RESULTS & VALIDATION ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Deliverables</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Demo</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Performance</span>
</div>

#### PENQUEST Platform Demonstration

<div class="flex justify-center items-center h-full -mt-6">
  <video controls class="max-w-full max-h-[45vh] object-contain rounded shadow">
    <source src="/PENQUEST Platform.mp4" type="video/mp4">
    Your browser does not support the video tag.
  </video>
</div>

<div class="text-center mt-6">
  <div class="bg-purple-100 p-4 rounded-lg inline-block">
    <p class="text-lg font-bold text-purple-800">🎥 Live Platform Demonstration - Complete User Journey</p>
  </div>
</div>

<!--
video demo of implementation
-->

---

### SECTION 5: RESULTS & VALIDATION ANALYSIS

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Deliverables</span> <span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Demo</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Performance</span>
</div>

#### Platform Performance & Scalability Validation

<div class="grid grid-cols-2 gap-8 max-h-[55vh] overflow-hidden">
  <div class="bg-orange-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-orange-700 mb-4"> Validated Capacity Analysis</h5>
    <div class="space-y-2 text-sm">
      <div class="bg-white p-2 rounded border border-orange-200">
        <div class="font-semibold text-orange-800">8GB RAM: 7 users</div>
        <div class="text-gray-600">Small classroom/lab</div>
      </div>
      <div class="bg-white p-2 rounded border border-orange-200">
        <div class="font-semibold text-orange-800">16GB RAM: 20 users</div>
        <div class="text-gray-600">Standard classroom</div>
      </div>
      <div class="bg-white p-2 rounded border border-orange-200">
        <div class="font-semibold text-orange-800">32GB RAM: 45 users</div>
        <div class="text-gray-600">Large workshop</div>
      </div>
      <div class="bg-white p-2 rounded border border-orange-200">
        <div class="font-semibold text-orange-800">64GB RAM: 95 users</div>
        <div class="text-gray-600">Competition scale</div>
      </div>
    </div>
  </div>
  
  <div class="bg-teal-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-teal-700 mb-4"> Measured Performance</h5>
    <div class="space-y-3 text-sm">
      <div class="bg-white p-3 rounded border border-teal-200">
        <div class="font-semibold text-teal-800">Container Startup: 15 seconds</div>
        <div class="text-gray-600">Faster than 30s target</div>
      </div>
      <div class="bg-white p-3 rounded border border-teal-200">
        <div class="font-semibold text-teal-800">Core Platform: 3.2GB</div>
        <div class="text-gray-600">Base infrastructure overhead</div>
      </div>
      <div class="bg-white p-3 rounded border border-teal-200">
        <div class="font-semibold text-teal-800">Per User: 640MB</div>
        <div class="text-gray-600">512MB Desktop + 128MB Challenge</div>
      </div>
      <div class="bg-white p-3 rounded border border-teal-200">
        <div class="font-semibold text-teal-800">Linear Scalability</div>
        <div class="text-gray-600">Memory-based capacity formula</div>
      </div>
    </div>
  </div>
</div>

<!--
platform is perfomant since container can spins up quickly

platform is scalable : more ram we add , more users we can have
-->

---
layout: section
background: 'linear-gradient(135deg, #6366f1, #4f46e5)'
---

<div class="section-header text-center">
  <h1 class="text-8xl font-bold text-white mb-4">Section 6</h1>
  <h2 class="text-3xl font-semibold text-white">Conclusion & Future Perspectives</h2>
</div>

<div class="progress-nav">
  <div class="nav-item completed">1. Context</div>
  <div class="nav-item completed">2. Foundation</div>
  <div class="nav-item completed">3. Design</div>
  <div class="nav-item completed">4. Implementation</div>
  <div class="nav-item completed">5. Results</div>
  <div class="nav-item active">6. Conclusion</div>
</div>

---

### SECTION 6: CONCLUSION & FUTURE PERSPECTIVES

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Conclusion</span> <span style="background-color: #9ca3af; color: white; padding: 6px 12px; border-radius: 6px; margin: 2px; display: inline-block; font-size: 14px;">Future Perspectives</span>
</div>

#### Project Achievements & Impact

<div class="grid grid-cols-2 gap-8 max-h-[55vh] overflow-hidden">
  <div class="bg-green-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-green-700 mb-4"> Key Achievements</h5>
    <div class="space-y-3 text-sm">
      <div class="bg-white p-3 rounded border border-green-200">
        <div class="font-semibold text-green-800">PENQUEST Platform Delivered</div>
        <div class="text-gray-600">Advanced cybersecurity training platform</div>
      </div>
      <div class="bg-white p-3 rounded border border-green-200">
        <div class="font-semibold text-green-800">Four Core Components</div>
        <div class="text-gray-600">CTFd + Whale + Web Desktop + Security Monitor</div>
      </div>
      <div class="bg-white p-3 rounded border border-green-200">
        <div class="font-semibold text-green-800">Security Framework Integration</div>
        <div class="text-gray-600">OWASP, CWE, MITRE ATT&CK, CVSS</div>
      </div>
    </div>
  </div>
  
  <div class="bg-blue-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-blue-700 mb-4"> Impact & Validation</h5>
    <div class="space-y-3 text-sm">
      <div class="bg-white p-3 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">Workforce Gap Solution</div>
        <div class="text-gray-600">Bridge theory-practice gap</div>
      </div>
      <div class="bg-white p-3 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">Industry Validation</div>
        <div class="text-gray-600">DATAPROTECT organizational context</div>
      </div>
      <div class="bg-white p-3 rounded border border-blue-200">
        <div class="font-semibold text-blue-800">Scalable Architecture</div>
        <div class="text-gray-600">7-95 users, workshop to conference</div>
      </div>
    </div>
  </div>
</div>

<!--
penquest is cybersec training platform that integrates 4core compoents and sec framworks , all that to bridhe theory with practice
-->

---

### SECTION 6: CONCLUSION & FUTURE PERSPECTIVES

<div style="text-align: center; margin: 16px 0;">
<span style="background-color: #10b981; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Conclusion</span> <span style="background-color: #3b82f6; color: white; padding: 6px 12px; border-radius: 6px; font-weight: bold; margin: 2px; display: inline-block; font-size: 14px;">Future Perspectives</span>
</div>

#### Research Directions & Enhancement Opportunities

<div class="grid grid-cols-2 gap-8 max-h-[55vh] overflow-hidden">
  <div class="bg-purple-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-purple-700 mb-4"> AI & Analytics Integration</h5>
    <div class="space-y-3 text-sm">
      <div class="bg-white p-3 rounded border border-purple-200">
        <div class="font-semibold text-purple-800">Machine Learning Analytics</div>
        <div class="text-gray-600">Personalized learning paths</div>
      </div>
      <div class="bg-white p-3 rounded border border-purple-200">
        <div class="font-semibold text-purple-800">Intelligent Tutoring</div>
        <div class="text-gray-600">Adaptive difficulty & real-time guidance</div>
      </div>
      <div class="bg-white p-3 rounded border border-purple-200">
        <div class="font-semibold text-purple-800">Performance Patterns</div>
        <div class="text-gray-600">Student progress analysis</div>
      </div>
    </div>
  </div>
  
  <div class="bg-orange-50 p-6 rounded-lg">
    <h5 class="text-xl font-bold text-orange-700 mb-4"> Platform Evolution</h5>
    <div class="space-y-3 text-sm">
      <div class="bg-white p-3 rounded border border-orange-200">
        <div class="font-semibold text-orange-800">Collaborative Learning</div>
        <div class="text-gray-600">Shared virtual environments</div>
      </div>
      <div class="bg-white p-3 rounded border border-orange-200">
        <div class="font-semibold text-orange-800">Extended Security Coverage</div>
        <div class="text-gray-600">Cloud, IoT, AI security domains</div>
      </div>
      <div class="bg-white p-3 rounded border border-orange-200">
        <div class="font-semibold text-orange-800">Industry-Academia Model</div>
        <div class="text-gray-600">Broader adoption framework</div>
      </div>
    </div>
  </div>
</div>

<!--
plattform could integrates AI via...

and enables coloborative learning



PENQUEST's future roadmap focuses on AI-enhanced personalization through machine learning analytics, intelligent tutoring, and adaptive learning paths, while expanding platform capabilities to include collaborative environments, extended security domains (cloud, IoT, AI), and industry-academia integration. This evolution transforms PENQUEST into a comprehensive, intelligent cybersecurity education ecosystem that bridges academic learning with industry needs, addressing the global cybersecurity workforce shortage through innovative educational technology.
-->

---

<div class="text-center h-full flex flex-col justify-center items-center">
  <h1 class="text-5xl font-bold text-blue-600 mb-6">Thank You for Your Attention!</h1>
  <p class="text-2xl text-gray-600 mb-8">Questions & Discussion</p>
  <div class="text-lg text-gray-500">
    PENQUEST Platform - Cybersecurity Education Innovation
  </div>
</div>

---
layout: cover
background: '#f8f9fa'
css: unocss
---

<style>
/* Import our custom presentation styles */
@import './pfe-presentation-styles.css';

/* Universal Slide Container Framework - Prevents Cutoff Issues */
.slide-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.slide-header {
  flex-shrink: 0;
  z-index: 10;
}

.slide-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  overflow: hidden;
}

/* Content Type Utilities */
.slide-image {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 0;
}

.slide-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  object-position: center;
}

.slide-table {
  flex: 1;
  overflow: auto;
  min-height: 0;
}

.slide-table table {
  width: 100%;
  font-size: 0.875rem;
  border-collapse: collapse;
}

.slide-grid {
  flex: 1;
  display: grid;
  gap: 1rem;
  align-content: start;
  min-height: 0;
}

.slide-text {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

/* Auto-scaling utilities */
.auto-scale {
  font-size: clamp(0.75rem, 2vw, 1rem);
}

.auto-scale-large {
  font-size: clamp(1rem, 3vw, 1.5rem);
}

/* Grid variations */
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive breakpoints */
@media (max-width: 768px) {
  .grid-4 { grid-template-columns: repeat(2, 1fr); }
  .grid-3 { grid-template-columns: repeat(2, 1fr); }
}
</style>

<div style="position: absolute; top: 10px; left: 20px; right: 20px; display: flex; justify-content: space-between; align-items: flex-start;">
  <img src="/school-logo.png" alt="ESI Logo" style="height: 54px;">
  <img src="/company-logo.png" alt="DATAPROTECT Logo" style="height: 46px;">
</div>


<div style="position: absolute; top: 64px; left: 50%; transform: translateX(-50%); text-align: center;">
  <div style="color: black; font-size: 14px; font-weight: 600; line-height: 1.3;">
    End-of-Studies Project Defense<br>
    For Obtaining the State Engineer Diploma<br>
    <span style="font-size: 12px;">Specialization: Information Systems Security Engineering and Cyberdefense</span>
  </div>
</div>

<div class="text-center mb-8 mt-16">
</div>

<div class="text-center mb-8 mt-16">
  <h1 style="color: black; font-size: 28px; line-height: 1.2; font-weight: 600; margin: 0;">
    Development of a Platform for Cybersecurity Challenges:<br>
    Simulating Real-World Web Vulnerabilities
  </h1>
  <div style="color: #1a73e8; font-size: 20px; font-weight: 600; margin-top: 16px;">
    PENQUEST Platform
  </div>
  <hr class="border-blue-600 border-2 w-3/4 mx-auto mt-4">
</div>

<div class="flex justify-between mt-16 text-xs px-4">
  <div class="text-left" style="width: 30%;">
    <div class="font-bold mb-2">Supervised by:</div>
    <div class="space-y-1">
      <div>Dr. Ali EL KSIMI – ESI</div>
      <div>Dr. Imad ABDESSADKI – DATAPROTECT</div>
      <div>Mr. Ayoub HRAZEM – DATAPROTECT</div>
      <div>Mr. Darhbar Zine elabidine – DATAPROTECT</div>
    </div>
  </div>
  
  <div class="text-center" style="width: 25%;">
    <div class="font-bold mb-2">Presented by:</div>
    <div>Mr. BAJJI Ilyas</div>
  </div>
  
  <div class="text-left" style="width: 30%;">
    <div class="font-bold mb-2">Defense Committee:</div>
    <div class="space-y-1">
      <div>Dr. Rachid GOUARTI – President</div>
      <div>Dr. Ali EL KSIMI – Member</div>
      <div>Dr. Imad ABDESSADKI – Member</div>
    </div>
  </div>
</div>

<div class="absolute bottom-0 left-0 right-0 flex justify-center bg-blue-600 text-white px-6 py-2 text-xs">
  <span class="font-bold">2024-2025</span>
</div>
