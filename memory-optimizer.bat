@echo off
echo MEMORY OPTIMIZATION MENU
echo ========================
echo.
echo Choose memory allocation level:
echo.
echo 1. STANDARD (2GB) - For basic presentations
echo 2. HIGH (4GB) - For image-heavy presentations  
echo 3. MAXIMUM (8GB) - For complex presentations with videos
echo 4. SYSTEM INFO - Check available memory
echo 5. Exit
echo.
set /p choice="Enter choice (1-5): "

if "%choice%"=="1" goto standard
if "%choice%"=="2" goto high  
if "%choice%"=="3" goto maximum
if "%choice%"=="4" goto sysinfo
if "%choice%"=="5" goto exit

:standard
echo Starting with STANDARD memory (2GB)...
set NODE_OPTIONS=--max-old-space-size=2048
npm run dev-fast
goto end

:high
echo Starting with HIGH memory (4GB)...
set NODE_OPTIONS=--max-old-space-size=4096 --max-semi-space-size=512
npm run dev -- --host --port 3030
goto end

:maximum
echo Starting with MAXIMUM memory (8GB)...
set NODE_OPTIONS=--max-old-space-size=8192 --max-semi-space-size=1024 --optimize-for-size
npm run dev -- --host --port 3030
goto end

:sysinfo
echo.
echo SYSTEM MEMORY INFORMATION:
echo ===========================
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:list
echo.
echo Press any key to return to menu...
pause > nul
goto start

:exit
exit

:end
pause
