#!/usr/bin/env python3
"""
PDF to PNG Converter for PENQUEST Diagrams
Converts all PDF files to high-quality PNG images using PyMuPDF
No Poppler installation required!

Usage: python convert_pdfs_to_png.py
"""

import os
import sys
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def install_requirements():
    """Install required packages"""
    required_packages = ["PyMuPDF", "Pillow"]
    
    for package in required_packages:
        try:
            if package == "PyMuPDF":
                import fitz
                print(f"✅ {package} is already installed")
            elif package == "Pillow":
                from PIL import Image
                print(f"✅ {package} is already installed")
        except ImportError:
            print(f"📦 Installing {package}...")
            os.system(f"{sys.executable} -m pip install {package}")
            print(f"✅ {package} installed successfully!")

def convert_pdf_to_png(pdf_path, output_path=None, zoom=2.5):
    """
    Convert PDF to PNG using PyMuPDF
    
    Args:
        pdf_path (str): Path to PDF file
        output_path (str): Output PNG path
        zoom (float): Zoom factor for quality (2.5 = ~180 DPI for presentations)
    
    Returns:
        str: Path to generated PNG file or None if failed
    """
    try:
        import fitz  # PyMuPDF
        
        logger.info(f"Converting: {Path(pdf_path).name}")
        
        # Open PDF document
        pdf_document = fitz.open(pdf_path)
        
        if pdf_document.page_count == 0:
            logger.error(f"No pages found in PDF: {pdf_path}")
            pdf_document.close()
            return None
        
        # Get the first page
        first_page = pdf_document[0]
        
        # Create transformation matrix for higher resolution
        mat = fitz.Matrix(zoom, zoom)
        
        # Render page to pixmap (image)
        pix = first_page.get_pixmap(matrix=mat)
        
        # If no output path specified, create one in same directory
        if output_path is None:
            pdf_file = Path(pdf_path)
            output_path = pdf_file.parent / f"{pdf_file.stem}.png"
        
        # Save as PNG
        pix.save(str(output_path))
        
        # Get file size for reporting
        size_mb = Path(output_path).stat().st_size / (1024 * 1024)
        
        # Clean up
        pdf_document.close()
        
        logger.info(f"✅ Created: {Path(output_path).name} ({size_mb:.1f} MB)")
        return str(output_path)
        
    except Exception as e:
        logger.error(f"❌ Error converting {pdf_path}: {str(e)}")
        return None

def find_all_pdfs(directory):
    """
    Find all PDF files recursively in directory
    
    Args:
        directory (str): Directory path to search
        
    Returns:
        list: List of PDF file paths
    """
    pdf_files = []
    directory_path = Path(directory)
    
    if not directory_path.exists():
        logger.error(f"Directory does not exist: {directory}")
        return pdf_files
    
    # Recursively find all PDF files
    for pdf_file in directory_path.rglob("*.pdf"):
        pdf_files.append(str(pdf_file))
    
    # Sort for consistent output
    pdf_files.sort()
    
    logger.info(f"Found {len(pdf_files)} PDF files")
    return pdf_files

def convert_all_pdfs(base_directory, zoom=2.5, skip_existing=True):
    """
    Convert all PDFs in directory to PNG
    
    Args:
        base_directory (str): Directory to search for PDFs
        zoom (float): Quality factor (2.5 = good for presentations)
        skip_existing (bool): Skip conversion if PNG already exists
        
    Returns:
        dict: Summary of conversion results
    """
    results = {
        'total_found': 0,
        'converted': 0,
        'skipped': 0,
        'failed': 0,
        'converted_files': [],
        'failed_files': []
    }
    
    # Find all PDF files
    pdf_files = find_all_pdfs(base_directory)
    results['total_found'] = len(pdf_files)
    
    if not pdf_files:
        print("❌ No PDF files found in the directory!")
        return results
    
    print(f"🔄 Converting {len(pdf_files)} PDF files to PNG...")
    print(f"🎯 Quality: High ({zoom}x zoom ≈ {int(zoom * 72)} DPI)")
    print("=" * 70)
    
    for i, pdf_path in enumerate(pdf_files, 1):
        pdf_file = Path(pdf_path)
        png_path = pdf_file.parent / f"{pdf_file.stem}.png"
        
        # Show relative path for cleaner output
        rel_path = pdf_file.relative_to(Path(base_directory))
        print(f"[{i:2d}/{len(pdf_files)}] {rel_path}")
        
        # Check if PNG already exists
        if skip_existing and png_path.exists():
            print(f"        ⏭️  PNG already exists, skipping")
            results['skipped'] += 1
            continue
        
        # Convert PDF to PNG
        output_file = convert_pdf_to_png(pdf_path, str(png_path), zoom)
        
        if output_file:
            results['converted'] += 1
            results['converted_files'].append(str(png_path))
            print(f"        ✅ Success!")
        else:
            results['failed'] += 1
            results['failed_files'].append(str(pdf_path))
            print(f"        ❌ Failed!")
        
        print()  # Empty line for readability
    
    return results

def print_summary(results):
    """Print detailed conversion summary"""
    print("=" * 70)
    print("📊 CONVERSION SUMMARY")
    print("=" * 70)
    print(f"📁 Total PDFs found:      {results['total_found']}")
    print(f"✅ Successfully converted: {results['converted']}")
    print(f"⏭️  Skipped (PNG exists):   {results['skipped']}")
    print(f"❌ Failed conversions:     {results['failed']}")
    print("=" * 70)
    
    if results['converted'] > 0:
        print("\n🎉 NEW PNG FILES CREATED:")
        for png_path in results['converted_files']:
            png_file = Path(png_path)
            print(f"   📄 {png_file.name}")
    
    if results['failed'] > 0:
        print("\n⚠️  FAILED CONVERSIONS:")
        for pdf_path in results['failed_files']:
            pdf_file = Path(pdf_path)
            print(f"   📄 {pdf_file.name}")

def show_update_instructions(results):
    """Show how to update slides.md files"""
    if results['converted'] == 0:
        return
    
    print("\n" + "=" * 70)
    print("📝 HOW TO UPDATE YOUR SLIDES.MD")
    print("=" * 70)
    print("Replace PDF references with PNG references in your slides:")
    print()
    
    # Show examples from converted files
    examples = results['converted_files'][:3]  # Show first 3
    for png_path in examples:
        png_file = Path(png_path)
        pdf_name = f"{png_file.stem}.pdf"
        
        print(f"BEFORE:")
        print(f'  <img src="/figures/.../{ pdf_name}" alt="...">')
        print(f"AFTER:")
        print(f'  <img src="/figures/.../{ png_file.name}" alt="...">')
        print()
    
    if len(results['converted_files']) > 3:
        print(f"... and {len(results['converted_files']) - 3} more files")
    
    print("💡 TIP: Use Find & Replace in your editor:")
    print("   Find:    .pdf")
    print("   Replace: .png")

def main():
    """Main execution function"""
    print("🚀 PENQUEST PDF to PNG Converter")
    print("=" * 70)
    print("✅ High-quality conversion for web presentations")
    print("✅ No Poppler installation required (uses PyMuPDF)")
    print("✅ Preserves directory structure")
    print("=" * 70)
    
    # Get the script directory and target diagrams directory
    script_dir = Path(__file__).parent
    diagrams_dir = script_dir / "figures" / "diagrams-gant-and-uml"
    
    print(f"📂 Script location: {script_dir}")
    print(f"📂 Target directory: {diagrams_dir}")
    print()
    
    # Check if target directory exists
    if not diagrams_dir.exists():
        print(f"❌ Target directory not found: {diagrams_dir}")
        print("💡 Make sure you're running this script from the correct location")
        input("Press Enter to exit...")
        return
    
    try:
        # Install required packages
        print("🔧 Checking requirements...")
        install_requirements()
        print()
        
        # Import after installation
        import fitz
        print("🎯 Starting PDF conversion process...")
        print()
        
        # Convert all PDFs
        results = convert_all_pdfs(
            str(diagrams_dir),
            zoom=2.5,  # Good quality for presentations
            skip_existing=True
        )
        
        # Show summary
        print_summary(results)
        
        # Show update instructions
        if results['converted'] > 0:
            show_update_instructions(results)
            print(f"\n🏁 Conversion complete! {results['converted']} new PNG files ready to use.")
        else:
            print("\n🏁 No new conversions needed.")
        
        print("\n🎉 Your presentation images are now web-ready!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Try running: pip install PyMuPDF Pillow")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\nPress Enter to exit...")
    input()

if __name__ == "__main__":
    main()
