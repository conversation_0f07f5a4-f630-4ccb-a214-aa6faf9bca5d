# 🎯 SLIDEV REMOTE PRESENTATION SETUP GUIDE

## Problem Description
When presenting with Slide<PERSON>, you have two tabs:
- **Presenter Mode**: Where you control the presentation (with notes, controls)
- **Audience Mode**: What the audience sees

When sharing the audience tab via Discord/screen sharing, the audience doesn't see slides move because the tabs aren't synchronized.

## Solutions

### Option 1: Use Remote Access (Recommended)

#### Step 1: Start with Remote Access
```bash
# Start Slidev with remote access enabled
npm run dev-remote

# Or with password protection
npm run dev-remote-password
```

#### Step 2: Access URLs
After starting, you'll see URLs like:
```
Local:    http://localhost:3030/
Network:  http://*************:3030/
```

#### Step 3: Open Two Tabs
1. **Presenter Tab**: `http://localhost:3030/presenter` (for you)
2. **Audience Tab**: `http://localhost:3030/` (share this screen)

#### Step 4: Present
- Control slides from the presenter tab
- Share the audience tab screen via Discord
- Slides will automatically sync between tabs!

### Option 2: Use Single Tab with Presenter View

#### Alternative Approach
1. Open: `http://localhost:3030/presenter`
2. Use the "Present" button in presenter mode
3. Share the presentation window (not the presenter controls)

### Option 3: Use Tunnel for Remote Access

If you need to share with remote participants:
```bash
# Start with tunnel (creates public URL)
npm run dev -- --remote --tunnel
```

## Configuration Files Updated

### package.json
Added new scripts:
- `dev-remote`: Basic remote access
- `dev-remote-password`: Remote access with password protection

### slidev.config.ts
Added:
- `presenter: true` - Enables presenter mode

## Usage Instructions

1. **For Local Presentation (Discord screen share):**
   ```bash
   npm run dev-remote
   ```
   - Open presenter tab: `http://localhost:3030/presenter`
   - Share audience tab: `http://localhost:3030/`

2. **For Remote Presentation:**
   ```bash
   npm run dev-remote-password
   ```
   - Password: `presentation2025`
   - Share the network URL with participants

3. **For Public Presentation:**
   ```bash
   npm run dev -- --remote --tunnel
   ```
   - Creates a public Cloudflare tunnel URL

## Keyboard Shortcuts in Presenter Mode
- `Arrow Keys`: Navigate slides
- `F`: Toggle fullscreen
- `O`: Toggle overview
- `D`: Toggle dark mode
- `G`: Go to specific slide

## Troubleshooting

### Issue: Slides still not syncing
**Solution**: Make sure you're using the correct URLs:
- Presenter: `http://localhost:3030/presenter`
- Audience: `http://localhost:3030/`

### Issue: Can't access presenter mode
**Solution**: Check if password is required and enter it correctly.

### Issue: Network access not working
**Solution**: Check firewall settings and ensure the network URL is accessible.

## Best Practices

1. **Test before presentation**: Always test the setup before your actual presentation
2. **Use password protection**: When sharing publicly, use password protection
3. **Check network connectivity**: Ensure stable internet for remote access
4. **Have backup plan**: Keep a local copy ready in case of network issues

## Notes
- Remote access requires network connectivity between presenter and audience devices
- Password protection is recommended for security
- The tunnel option creates a temporary public URL that expires when you stop the server
