<template>
  <div 
    v-if="$nav.currentPage > 1 && $nav.currentPage < $nav.total"
    class="slide-number"
    :style="{
      position: 'fixed',
      bottom: '1rem',
      right: '1rem',
      fontSize: '0.75rem',
      color: '#6b7280',
      background: 'rgba(255, 255, 255, 0.8)',
      padding: '0.25rem 0.5rem',
      borderRadius: '0.25rem',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      zIndex: 100
    }"
  >
    {{ $nav.currentPage }}
  </div>
</template>

<script setup>
// Access Slidev's navigation context
// $nav.currentPage = current slide number
// $nav.total = total number of slides
</script>
