/* Global slide number styling */
.slidev-layout {
  counter-increment: slide;
}

.slidev-layout::after {
  content: counter(slide);
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  font-size: 0.4rem !important;
  color: #ff0000 !important;
  background: rgba(255, 255, 255, 0.8);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Hide slide number on cover slide */
.slidev-layout.cover::after {
  display: none;
}

/* White text on dark section slides */
.slidev-layout.section::after {
  color: white;
  background: rgba(0, 0, 0, 0.3);
}

/* Initialize counter */
.slidev-slides {
  counter-reset: slide;
}
