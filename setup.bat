@echo off
echo 🚀 Setting up PENQUEST Presentation...

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    echo Visit: https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm is not installed. Please install npm first.
    pause
    exit /b 1
)

echo 📦 Installing dependencies...
npm install

if errorlevel 0 (
    echo ✅ Dependencies installed successfully!
    echo.
    echo 🎉 Setup complete! You can now run:
    echo    npm run dev     - Start development server
    echo    npm run build   - Build for production
    echo    npm run export  - Export to PDF
    echo.
    echo 🌐 Starting development server...
    npm run dev
) else (
    echo ❌ Installation failed. Please check the error messages above.
    pause
    exit /b 1
)
