<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PFE Presentation Template</title>
    <link rel="stylesheet" href="pfe-presentation-styles.css">
</head>
<body>

<!-- ================================
     EXAMPLE SLIDE WITH TRACKING
     ================================ -->

<!-- Main Section Navigation -->
<div class="section-nav">
    <div class="nav-item completed">1. Contexte</div>
    <div class="nav-item completed">2. Théorie</div>
    <div class="nav-item current">3. Conception</div>
    <div class="nav-item pending">4. Réalisation</div>
    <div class="nav-item pending">5. Recommandations</div>
    <div class="nav-item pending">6. Conclusion</div>
</div>

<!-- Progress Bar -->
<div class="progress-container">
    <div class="progress-bar">
        <div class="progress-fill" style="width: 50%;"></div>
    </div>
    <div class="progress-text">50% Complete - Section 3 sur 6</div>
</div>

<!-- Section Header -->
<div class="section-header slide-in">
    <span class="section-number">3</span>
    <span class="section-title">Conception de la solution</span>
</div>

<!-- Subsection Navigation -->
<div class="subsection-nav">
    <div class="subsection-item completed">3.1 Architecture générale</div>
    <div class="subsection-item completed">3.2 Données structurées</div>
    <div class="subsection-item current">3.3 Données images</div>
    <div class="subsection-item pending">3.4 Données textuelles</div>
</div>

<!-- Content Area -->
<div class="content">
    <h2>3.3 Description des images des voitures</h2>
    
    <div class="solution-highlight">
        <h3>Pipeline de traitement d'images</h3>
        <p>Trois tâches principales identifiées :</p>
        <ul>
            <li><strong>Détection de dommage</strong> - Confirmer l'existence du dommage</li>
            <li><strong>Localisation</strong> - Détecter l'emplacement du dommage</li>
            <li><strong>Gravité</strong> - Évaluer la sévérité des dégâts</li>
        </ul>
    </div>

    <div class="metric-box">
        <span class="metric-value">89%</span>
        <span class="metric-label">Précision Détection</span>
    </div>
    
    <div class="metric-box">
        <span class="metric-value">80%</span>
        <span class="metric-label">Précision Localisation</span>
    </div>
    
    <div class="metric-box">
        <span class="metric-value">74%</span>
        <span class="metric-label">Précision Gravité</span>
    </div>
</div>

<!-- ================================
     JAVASCRIPT FOR DYNAMIC UPDATES
     ================================ -->

<script>
// Navigation state management
class PresentationTracker {
    constructor() {
        this.sections = [
            { id: 1, name: "Contexte", status: "completed" },
            { id: 2, name: "Théorie", status: "completed" },
            { id: 3, name: "Conception", status: "current" },
            { id: 4, name: "Réalisation", status: "pending" },
            { id: 5, name: "Recommandations", status: "pending" },
            { id: 6, name: "Conclusion", status: "pending" }
        ];
        this.currentSection = 3;
    }

    // Update progress when moving to next section
    goToSection(sectionId) {
        // Mark previous sections as completed
        this.sections.forEach(section => {
            if (section.id < sectionId) {
                section.status = "completed";
            } else if (section.id === sectionId) {
                section.status = "current";
            } else {
                section.status = "pending";
            }
        });
        
        this.currentSection = sectionId;
        this.updateUI();
    }

    // Update the UI elements
    updateUI() {
        // Update navigation
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach((item, index) => {
            const section = this.sections[index];
            item.className = `nav-item ${section.status}`;
        });

        // Update progress bar
        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');
        const completedSections = this.sections.filter(s => s.status === 'completed').length;
        const progress = (completedSections / this.sections.length) * 100;
        
        progressFill.style.width = `${progress}%`;
        progressText.textContent = `${Math.round(progress)}% Complete - Section ${this.currentSection} sur ${this.sections.length}`;
    }

    // Get current section info
    getCurrentSectionInfo() {
        return this.sections.find(s => s.status === 'current');
    }
}

// Initialize tracker
const tracker = new PresentationTracker();

// Keyboard navigation
document.addEventListener('keydown', function(event) {
    if (event.key === 'ArrowRight') {
        // Go to next section
        if (tracker.currentSection < tracker.sections.length) {
            tracker.goToSection(tracker.currentSection + 1);
        }
    } else if (event.key === 'ArrowLeft') {
        // Go to previous section
        if (tracker.currentSection > 1) {
            tracker.goToSection(tracker.currentSection - 1);
        }
    }
});

// Click navigation
document.querySelectorAll('.nav-item').forEach((item, index) => {
    item.addEventListener('click', () => {
        tracker.goToSection(index + 1);
    });
});
</script>

</body>
</html>
