# 🎨 PFE PRESENTATION: TYPOGRAPHY & SLIDE ORDER GUIDE

## **📝 TYPOGRAPHY SPECIFICATIONS**

### **🔤 FONT FAMILIES**
```css
/* Primary Font Stack */
--primary-font: 'Segoe UI', 'Arial', 'Helvetica Neue', sans-serif;
--heading-font: 'Calibri', 'Segoe UI', sans-serif;
--code-font: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;
--accent-font: 'Trebuchet MS', 'Arial', sans-serif;
```

### **📏 FONT SIZE HIERARCHY**

#### **Title Slide (Slide 1):**
```css
.main-title {
    font-size: 36px;          /* Titre principal */
    font-weight: 700;
    line-height: 1.2;
}

.subtitle {
    font-size: 24px;          /* Sous-titre */
    font-weight: 500;
    line-height: 1.3;
}

.institution-info {
    font-size: 16px;          /* Informations institution */
    font-weight: 400;
}

.author-info {
    font-size: 20px;          /* Nom de l'auteur */
    font-weight: 600;
}

.jury-info {
    font-size: 14px;          /* Membres du jury */
    font-weight: 400;
    line-height: 1.4;
}

.date-info {
    font-size: 16px;          /* Date de soutenance */
    font-weight: 500;
}
```

#### **Content Slides:**
```css
.section-header {
    font-size: 32px;          /* En-tête de section */
    font-weight: 700;
}

.section-number {
    font-size: 28px;          /* Numéro de section */
    font-weight: 800;
}

.slide-title {
    font-size: 28px;          /* Titre de diapositive */
    font-weight: 600;
}

.subsection-title {
    font-size: 22px;          /* Titre de sous-section */
    font-weight: 600;
}

.content-text {
    font-size: 18px;          /* Texte principal */
    font-weight: 400;
    line-height: 1.5;
}

.bullet-text {
    font-size: 16px;          /* Puces */
    font-weight: 400;
    line-height: 1.6;
}

.caption-text {
    font-size: 14px;          /* Légendes */
    font-weight: 400;
    font-style: italic;
}

.code-text {
    font-size: 14px;          /* Code */
    font-family: var(--code-font);
    line-height: 1.4;
}

.table-header {
    font-size: 16px;          /* En-têtes de tableau */
    font-weight: 600;
}

.table-content {
    font-size: 14px;          /* Contenu de tableau */
    font-weight: 400;
}
```

#### **Special Elements:**
```css
.metric-value {
    font-size: 32px;          /* Valeurs métriques importantes */
    font-weight: 800;
}

.metric-label {
    font-size: 12px;          /* Étiquettes métriques */
    font-weight: 500;
    text-transform: uppercase;
}

.quote-text {
    font-size: 20px;          /* Citations */
    font-style: italic;
    font-weight: 400;
}

.navigation-text {
    font-size: 14px;          /* Navigation */
    font-weight: 500;
}
```

---

## **📑 FIRST SLIDE STRUCTURE**

### **🎯 SLIDE 1: PAGE DE TITRE**

```html
<!DOCTYPE html>
<html>
<head>
    <title>PFE Title Slide</title>
</head>
<body>

<!-- SLIDE 1: TITLE SLIDE -->
<div class="title-slide">
    
    <!-- Header avec institutions -->
    <div class="header-institutions">
        <div class="left-institution">
            <img src="logo-institution.png" alt="Institution Logo">
            <div class="institution-text">
                <p class="country">Royaume du Maroc</p>
                <p class="ministry">Haut-Commissariat au Plan</p>
                <p class="school">École des Sciences de l'Information</p>
            </div>
        </div>
        
        <div class="right-company">
            <img src="company-logo.png" alt="Company Logo">
            <p class="company-name">DEVOTEAM</p>
        </div>
    </div>

    <!-- Projet info -->
    <div class="project-info">
        <p class="project-type">Projet de fin d'étude pour l'obtention du titre :</p>
        <p class="degree-title">Ingénieur d'État des Données et des Connaissances</p>
    </div>

    <!-- Titre principal -->
    <div class="main-title-container">
        <h1 class="main-title">Détection de Fraude en Assurance Automobile</h1>
        <p class="subtitle">Utilisation de l'Intelligence Artificielle et du Machine Learning</p>
    </div>

    <!-- Informations de soutenance -->
    <div class="defense-info">
        <div class="left-info">
            <h3>Membres de jury :</h3>
            <ul class="jury-list">
                <li><strong>Président du jury :</strong> Pr. Mounia MIKRAM</li>
                <li><strong>Encadrante :</strong> Pr. Siham YOUSFI</li>
                <li><strong>Tuteur :</strong> M. Fahd KHAMLICHI IDRISSI</li>
            </ul>
        </div>
        
        <div class="right-info">
            <p class="defense-date">Soutenu le 23/07/2020 par:</p>
            <p class="author-name">Khadija IDMANSSOUR</p>
            <p class="academic-year">Année universitaire : 2019/2020</p>
        </div>
    </div>

</div>
</body>
</html>
```

---

## **📋 COMPLETE SLIDE ORDER**

### **🎯 PRESENTATION STRUCTURE (38+ slides)**

#### **INTRODUCTION (Slides 1-3)**
```
Slide 1:  📄 Page de titre
Slide 2:  📋 Plan de présentation / Table des matières
Slide 3:  🎯 Introduction générale et objectifs
```

#### **SECTION 1: CADRE CONTEXTUEL (Slides 4-12)**
```
Slide 4:  1️⃣ Section divider "Cadre contextuel du projet"
Slide 5:  🏢 1.1 Présentation de l'organisme d'accueil
Slide 6:  🏢 1.1 Services et activités de l'entreprise
Slide 7:  📋 1.2 Contexte du projet
Slide 8:  📋 1.2 Processus général des sinistres
Slide 9:  ⚠️ 1.3 Problématique - Problèmes identifiés
Slide 10: 💰 1.3 Impact financier et coûts
Slide 11: ❓ 1.3 Question de recherche
Slide 12: 🎯 1.4 Objectifs du projet
```

#### **SECTION 2: CADRE THÉORIQUE (Slides 13-17)**
```
Slide 13: 2️⃣ Section divider "Cadre théorique"
Slide 14: 📚 2.1 Latent Dirichlet Allocation (LDA)
Slide 15: 🧠 2.1 Dense Convolutional Networks (DenseNets)
Slide 16: 🏗️ 2.1 Architecture DenseNet-201
Slide 17: 📊 2.2 État de l'art - Tableau comparatif
```

#### **SECTION 3: CONCEPTION (Slides 18-25)**
```
Slide 18: 3️⃣ Section divider "Conception de la solution"
Slide 19: 🏗️ 3.1 Architecture générale
Slide 20: 📊 3.2 Description des données structurées
Slide 21: 📊 3.2 Pipeline données structurées
Slide 22: 🖼️ 3.3 Description des données images
Slide 23: 🖼️ 3.3 Pipeline traitement d'images
Slide 24: 🔤 3.4 Lecture des plaques d'immatriculation
Slide 25: 📝 3.5 Description des données textuelles
Slide 26: 📝 3.5 Pipeline analyse de textes
```

#### **SECTION 4: RÉALISATION (Slides 26-33)**
```
Slide 27: 4️⃣ Section divider "Réalisation de la solution"
Slide 28: ⚙️ 4.1 Prétraitement des données structurées
Slide 29: 🤖 4.2 Entraînement des modèles supervisés
Slide 30: 📈 4.3 Évaluation des modèles supervisés
Slide 31: 🖼️ 4.4 Construction des modèles d'images
Slide 32: 📊 4.5 Résultats modèles d'images
Slide 33: 🔤 4.6 Implémentation lecture de plaques
Slide 34: 📝 4.7 Application LDA sur les textes
Slide 35: 📊 4.8 Résultats analyse LDA
```

#### **SECTION 5: RECOMMANDATIONS (Slides 34-36)**
```
Slide 36: 5️⃣ Section divider "Recommandations"
Slide 37: 🔗 5.1 Architecture d'intégration
Slide 38: 💡 5.2 Solution d'intégration finale
```

#### **SECTION 6: CONCLUSION (Slides 37-39)**
```
Slide 39: 6️⃣ Section divider "Conclusion et perspectives"
Slide 40: ✅ 6.1 Conclusions et réalisations
Slide 41: 🚀 6.2 Perspectives et améliorations futures
```

#### **CLOSING (Slides 40-42)**
```
Slide 42: 🙏 Merci de votre attention
Slide 43: ❓ Questions et discussion
Slide 44: 📞 Informations de contact (optionnel)
```

---

## **🎨 CSS TYPOGRAPHY IMPLEMENTATION**

```css
/* ================================
   TYPOGRAPHY SYSTEM
   ================================ */

:root {
    /* Font families */
    --primary-font: 'Segoe UI', 'Arial', 'Helvetica Neue', sans-serif;
    --heading-font: 'Calibri', 'Segoe UI', sans-serif;
    --code-font: 'Consolas', 'Monaco', 'Courier New', monospace;
    
    /* Font sizes */
    --fs-mega: 36px;      /* Titre principal */
    --fs-xxl: 32px;       /* Section headers */
    --fs-xl: 28px;        /* Slide titles */
    --fs-lg: 24px;        /* Subtitles */
    --fs-md: 20px;        /* Important text */
    --fs-base: 18px;      /* Body text */
    --fs-sm: 16px;        /* Secondary text */
    --fs-xs: 14px;        /* Captions */
    --fs-xxs: 12px;       /* Labels */
    
    /* Font weights */
    --fw-light: 300;
    --fw-normal: 400;
    --fw-medium: 500;
    --fw-semibold: 600;
    --fw-bold: 700;
    --fw-extrabold: 800;
}

/* Base typography */
body {
    font-family: var(--primary-font);
    font-size: var(--fs-base);
    font-weight: var(--fw-normal);
    line-height: 1.6;
    color: #333;
}

/* Heading hierarchy */
h1 {
    font-family: var(--heading-font);
    font-size: var(--fs-mega);
    font-weight: var(--fw-bold);
    line-height: 1.2;
    margin-bottom: 1rem;
}

h2 {
    font-family: var(--heading-font);
    font-size: var(--fs-xl);
    font-weight: var(--fw-semibold);
    line-height: 1.3;
    margin-bottom: 0.8rem;
}

h3 {
    font-family: var(--heading-font);
    font-size: var(--fs-lg);
    font-weight: var(--fw-semibold);
    line-height: 1.4;
    margin-bottom: 0.6rem;
}

/* Specific slide elements */
.title-slide .main-title {
    font-size: var(--fs-mega);
    font-weight: var(--fw-bold);
    text-align: center;
    color: #2c3e50;
}

.title-slide .subtitle {
    font-size: var(--fs-lg);
    font-weight: var(--fw-medium);
    text-align: center;
    color: #7f8c8d;
    margin-top: 0.5rem;
}

.section-header {
    font-size: var(--fs-xxl);
    font-weight: var(--fw-bold);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
}

.slide-title {
    font-size: var(--fs-xl);
    font-weight: var(--fw-semibold);
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

/* Content elements */
.content-text {
    font-size: var(--fs-base);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.bullet-text {
    font-size: var(--fs-sm);
    line-height: 1.7;
}

.caption-text {
    font-size: var(--fs-xs);
    font-style: italic;
    color: #7f8c8d;
    text-align: center;
    margin-top: 0.5rem;
}

/* Code elements */
code, .code-text {
    font-family: var(--code-font);
    font-size: var(--fs-xs);
    background: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

pre code {
    display: block;
    padding: 1rem;
    overflow-x: auto;
}

/* Table typography */
table {
    font-size: var(--fs-sm);
}

th {
    font-weight: var(--fw-semibold);
    font-size: var(--fs-sm);
}

td {
    font-weight: var(--fw-normal);
    font-size: var(--fs-xs);
}

/* Metric boxes */
.metric-value {
    font-size: var(--fs-xxl);
    font-weight: var(--fw-extrabold);
    display: block;
}

.metric-label {
    font-size: var(--fs-xxs);
    font-weight: var(--fw-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Navigation elements */
.nav-item {
    font-size: var(--fs-xs);
    font-weight: var(--fw-medium);
}

.progress-text {
    font-size: var(--fs-xs);
    font-weight: var(--fw-semibold);
}

/* Responsive typography */
@media (max-width: 768px) {
    :root {
        --fs-mega: 28px;
        --fs-xxl: 24px;
        --fs-xl: 22px;
        --fs-lg: 20px;
        --fs-base: 16px;
        --fs-sm: 14px;
        --fs-xs: 12px;
    }
}

@media (max-width: 480px) {
    :root {
        --fs-mega: 24px;
        --fs-xxl: 20px;
        --fs-xl: 18px;
        --fs-lg: 16px;
        --fs-base: 14px;
        --fs-sm: 12px;
        --fs-xs: 10px;
    }
}
```

---

## **💡 ADDITIONAL RECOMMENDATIONS**

### **🎨 Visual Hierarchy:**
1. **Most Important** → Largest font, bold weight
2. **Important** → Large font, semi-bold
3. **Normal** → Base font, normal weight
4. **Secondary** → Smaller font, normal weight
5. **Captions** → Smallest font, italic

### **📏 Spacing Rules:**
- **Line height:** 1.2-1.3 for headings, 1.5-1.7 for body text
- **Margins:** Consistent spacing between elements
- **Padding:** Sufficient breathing room around text

### **🎯 Accessibility:**
- **Contrast ratio:** Minimum 4.5:1 for normal text
- **Font size:** Minimum 14px for body text
- **Line length:** Maximum 75 characters per line

### **📱 Responsive Design:**
- Scale fonts appropriately for different screen sizes
- Maintain readability on all devices
- Adjust spacing for mobile views

This comprehensive system ensures professional, readable, and visually appealing presentations! 🎓
