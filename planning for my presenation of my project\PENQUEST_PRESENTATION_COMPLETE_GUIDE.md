# 🎯 PENQUEST PRESENTATION GUIDE
## Development of a Platform for Cybersecurity Challenges: Simulating Real-World Web Vulnerabilities

---

## 📋 **PROJECT INFORMATION**

**Title**: Development of a Platform for Cybersecurity Challenges: Simulating Real-World Web Vulnerabilities  
**Platform**: PENQUEST - Advanced Cybersecurity Training Platform  
**Author**: <PERSON><PERSON>j<PERSON>  
**Degree**: State Engineer in Information Systems Security Engineering and Cyberdefense  
**Institution**: École des Sciences de l'Information (ESI) - Morocco  
**Host Organization**: DATAPROTECT  
**Duration**: 4 months (February - June 2025)  

**Supervisors**:
- **Academic**: Dr. <PERSON>
- **Industry**: Mr. <PERSON><PERSON><PERSON>, Dr. <PERSON><PERSON>, Mr. <PERSON><PERSON><PERSON> elabidine

**Jury Members**:
- **President**: Dr. <PERSON>SIMI
- **Members**: Dr. <PERSON><PERSON><PERSON>UARTI, Dr. <PERSON><PERSON> ABDESSADKI

---

## 📑 **PRESENTATION STRUCTURE (42 slides)**

### **🚀 OPENING SECTION (Slides 1-3)**

#### **Slide 1: Title Page**
- Institution headers with logos
- Main project title: "Development of a Platform for Cybersecurity Challenges"
- Subtitle: "Simulating Real-World Web Vulnerabilities" 
- PENQUEST platform name
- Author, supervisors, and jury information
- Academic year: 2024/2025

#### **Slide 2: Presentation Outline**
- **Clean Academic Design**: Professional typography with "Presentation Outline" title
- **6 Strategic Sections** with color-coded visual identity:
  - **Section 1**: Project Context and Global Challenge (Red gradient)
  - **Section 2**: Theoretical Foundation and Technology Analysis (Purple gradient)
  - **Section 3**: PENQUEST Design and Architecture (Blue gradient)
  - **Section 4**: Implementation and Technical Realization (Green gradient)
  - **Section 5**: Results and Educational Impact (Orange gradient)
  - **Section 6**: Conclusion and Future Perspectives (Indigo gradient)
- **Enhanced Spacing**: Clear visual separation between section numbers and titles
- **Simplified Focus**: Content-focused without slide count distractions

#### **Slide 3: Project Overview**
- **The Challenge**: Global shortage of cybersecurity professionals (4.8 million)
- **The Gap**: Critical disconnect between academic theory and practical skills
- **The Solution**: Advanced cybersecurity training platform with dynamic containerization
- **The Innovation**: Browser access + Dynamic containers + Real-time monitoring

---

### **1️⃣ SECTION 1: PROJECT CONTEXT AND GLOBAL CHALLENGE (Slides 4-12)**

#### **Slide 4: Section Divider**
- Section header with progress navigation
- Current section highlighted

#### **Slide 5: Global Cybersecurity Crisis**
- **Central Metric**: 4.8 MILLION global cybersecurity workforce shortage
- **Source**: ISC² 2024 Cybersecurity Workforce Study
- **Growth**: 19% increase from previous year
- **Impact**: >50% of significant incidents attributed to talent shortage
- **Projection**: Boston Consulting Group warns of critical situation
- **Consequences**: Organizations vulnerable, security costs rising, practical training deficit

#### **Slide 6: Traditional Education Limitations**
- **The Problem**: Theory-Practice Gap
- **Educational Challenges**:
  - Complex installations (local security tool configuration)
  - Scalability constraints (limited traditional CTF platforms)
  - Insufficient progress tracking (lack of educational analytics)
  - Missing framework integration (disconnection from industry standards)
- **Educational Disconnect**: Academic knowledge ≠ Operational skills
- **Critical Need**: Practical learning environments aligned with professional requirements

#### **Slide 7: DATAPROTECT - Host Organization**
- **Leadership Position**: Morocco's #1 cybersecurity company (Founded 2009)
- **Team**: 220+ specialized professionals, 580+ certifications collectively
- **Global Reach**: 450+ clients across 40 countries
- **Track Record**: 1,500+ cybersecurity projects completed
- **Revenue**: 217 million MAD (2021, 60% international)
- **International Presence**: Subsidiaries in Abidjan, Dubai, Paris

#### **Slide 8: DATAPROTECT Business Divisions**
- **7 Specialized Divisions**:
  - **Offensive Security**: 30+ consultants, penetration testing, 3,600+ person-days annually
  - **Cyberdefense Solutions**: 30+ consultants, 200+ vendor certifications
  - **Security Intelligence**: 24/7 SOC, 500,000+ events/second, 15 countries
  - **Governance, Risk & Compliance**: ISO 27001, GDPR, 6 PCI QSA professionals
  - **MSSP Division**: 60+ security infrastructures managed
  - **Cloud Cybersecurity**: Cloud-native protection services
  - **Cyber Resilience**: Business continuity and incident response

#### **Slide 9: Project Motivation at DATAPROTECT**
- **Identified Training Challenges**:
  - Scalable educational environments for diverse client programs
  - Varied training requirements (10-100+ participants)
  - Complex installation barriers limiting tool access
  - Limited tracking capabilities for educational assessment
- **Strategic Opportunity**: Leverage DATAPROTECT's expertise for educational innovation
- **Partnership Context**: Cybersecurity Skills Lab initiative, integration with client training programs

#### **Slide 10: PENQUEST Vision**
- **Core Concept**: "Advanced Cybersecurity Training Platform"
- **Technical Foundation**: CTFd Framework + Modern Containerization + Dynamic Orchestration
- **Key Innovations**:
  - **Browser-based Tool Access**: Eliminate installations, immediate Kali Linux access
  - **Dynamic Container Orchestration**: Instant deployment, automatic lifecycle management
  - **Real-time Security Monitoring**: Platform health without educational interference
  - **Industry Framework Integration**: OWASP Top 10, MITRE ATT&CK, CWE, CVSS alignment

#### **Slide 11: Project Objectives**
- **Technical Objectives**:
  - Extend CTFd capabilities with advanced containerization
  - Eliminate installation barriers through web-based tools
  - Integrate security frameworks (OWASP, MITRE ATT&CK, CWE, CVSS)
  - Provide scalable learning environments for diverse training needs
  - Bridge theory-practice gap through hands-on experiences
- **Educational Objectives**: Practical skill development, professional standards alignment, pedagogical assessment support
- **Expected Impact**: Significant contribution to resolving the shortage of 4.8 million cybersecurity professionals

#### **Slide 12: Development Context**
- **Project Timeline**: 4 months (February - June 2025), 7 structured phases
- **Environment**: DATAPROTECT cybersecurity expertise
- **Target**: Production-ready educational platform
- **Development Phases**:
  1. Requirements analysis and feasibility study
  2. CTFd foundation setup and Docker Swarm configuration
  3. ctfd-whale integration and network architecture
  4. Web desktop plugin development and container management
  5. Security monitoring implementation and dashboard creation
  6. Integration testing and system validation
  7. Deployment and documentation
- **Quality Assurance**: Unit, integration, system, and security testing

---

### **2️⃣ SECTION 2: THEORETICAL FOUNDATION AND TECHNOLOGY ANALYSIS (Slides 13-17)**

#### **Slide 13: Section Divider**
- Section 2 header with updated progress navigation

#### **Slide 14: CTF Platform Comparative Analysis**
- **Evaluation Methodology**: Multi-criteria assessment based on cybersecurity education research
- **Platform Comparison**:
  - **CTFd**: 9.2/10 (Comprehensive assessment, extensive plugins, excellent documentation)
  - **FBCTF**: 6.8/10 (Limited assessment, minimal plugins)
  - **RootTheBox**: 7.5/10 (Advanced features, moderate plugins)
- **CTFd Selection Rationale**: Superior plugin ecosystem, Flask architecture, active community, comprehensive assessment features
- **Competitive Advantage**: CTFd + ctfd-whale = Most extensible CTF platform for cybersecurity education

#### **Slide 15: Technology Stack Justification**
- **Containerization Platform**: Docker + Docker Swarm (proven orchestration, secure isolation)
- **Base Framework**: CTFd + ctfd-whale plugin (dynamic containers, lifecycle management)
- **Frontend/Backend**: React (component-based UI), FastAPI (high-performance API)
- **Network Access**: FRP (Fast Reverse Proxy), VNC/noVNC (browser-based desktop environments)
- **Monitoring**: Prometheus + Grafana + Loki (complete observability stack)
- **Advantages**: Resource management, environment consistency, scalability, rapid development

#### **Slide 16: Plugin Architecture Advantages**
- **Design Principles**:
  - **Modularity**: Independent development, functional isolation, selective deployment
  - **Extensibility**: Core platform enhancement without source code modification
  - **Maintainability**: Separation of concerns, CTFd upgrade compatibility
  - **Innovation**: Advanced features through systematic extension
- **Flask Foundation Architecture**:
  - Application Context: Deep integration with CTFd services
  - Blueprint Pattern: Modular route and view organization
  - Hook System: Request lifecycle observation without interference

#### **Slide 17: Security Framework Integration**
- **Integrated Industry Standards**:
  - **OWASP Top 10**: Web application security prioritization (A01-A10 contemporary risks)
  - **MITRE ATT&CK**: Realistic attack technique classification (Tactics → Techniques → Procedures)
  - **CWE**: Systematic software weakness enumeration (hierarchical vulnerability classification)
  - **CVSS**: Standardized vulnerability severity scoring (Base, Temporal, Environmental metrics)
- **Educational Alignment**: Professional certification preparation, industry relevance, structured pedagogical progression

---

### **3️⃣ SECTION 3: PENQUEST DESIGN AND ARCHITECTURE (Slides 18-26)**

#### **Slide 18: Section Divider**
- Section 3 header with updated progress navigation

#### **Slide 19: High-Level Platform Architecture**
- **Layered Architecture Approach**:
  - **Presentation Layer**: CTF Portal, Web Desktop, Admin interfaces
  - **Application Layer**: Challenge Tracking, Monitoring
  - **Service Layer**: CTFd Core, Whale Plugin
  - **Infrastructure Layer**: Docker Swarm, FRP, Storage
- **Separation of Educational Concerns**: Diverse user interfaces, educational business logic, specialized cybersecurity functionality
- **Transparent Integration**: Unified architecture maintaining CTFd compatibility with functional sophistication

#### **Slide 20: Plugin Ecosystem Architecture**
- **Main Components**:
  - **CTFd Core**: Base competition framework, authentication, challenges, scoring
  - **CTFd-Whale Plugin**: Dynamic container orchestration, Docker Swarm deployment, user isolation
  - **Web Desktop Plugin**: Browser-based cybersecurity tool access, Linux desktop environments
  - **Security Monitoring Plugin**: Real-time platform health, "transparent shield" monitoring
  - **Challenge Tracking Application**: Advanced analytics, React + FastAPI + framework integration
- **Ecosystem Integration**: Interdependent plugins creating sophisticated educational environment through simple composition

#### **Slide 21: Container Orchestration Design**
- **Dynamic Orchestration Flow**: User Request → CTFd Authentication → Whale Container Creation → FRP Proxy Configuration → Secure Container Access → Isolated Challenge Environment
- **Orchestration Components**:
  - **Docker Swarm**: Multi-node orchestration, service management, load balancing
  - **Network Isolation**: Overlay networks, per-user environments, automatic cleanup
  - **Resource Management**: Dynamic allocation, configurable limits, usage monitoring
- **Container Security**: Linux namespace isolation, privilege restrictions, private networks per session

#### **Slide 22: Web Desktop Integration**
- **Remote Access Architecture**:
  - **VNC Server**: Remote desktop protocol, complete graphical environments, pre-installed tools
  - **noVNC Client**: Browser access without installations, JavaScript/HTML5 client
  - **Template Management**: Standardized desktop templates, administrative configuration
  - **Session Management**: Persistent user sessions, automatic renewal, state saving
- **Educational Innovation**: Complete elimination of installation barriers enabling immediate access to professional cybersecurity tools

#### **Slide 23: Security Monitoring Architecture**
- **"Transparent Shield" Philosophy**: Platform monitoring while preserving learning authenticity
  - Infrastructure surveillance without educational interference
  - Threat detection distinguishing legitimate exploitation vs. platform compromise
  - Operational visibility maintaining learning context integrity
- **Monitoring Components**:
  - **Event Collection**: Flask request lifecycle hooks, pattern analysis, event classification
  - **Threat Detection**: Rate limiting, attack pattern recognition, severity alerts
  - **Dashboard Integration**: Real-time visibility, Prometheus metrics, Grafana dashboards
- **Monitoring-Education Balance**: Complete platform protection without compromising learning experience authenticity

#### **Slide 24: Challenge Tracking System**
- **Modern Application Architecture**:
  - **React Frontend**: Modern component interface, real-time filtering, progress visualization
  - **FastAPI Backend**: High-performance API, automatic documentation, data validation
- **Security Framework Integration**:
  - **Automatic Mapping**: OWASP Top 10, MITRE ATT&CK, CWE, CVSS
  - **Educational Analytics**: Progress tracking, completion statistics, learning gap identification
- **Educational Value**: Systematic alignment of challenges with cybersecurity professional standards

#### **Slide 25: Scalability Formula and Capacity Planning**
- **Mathematical Scalability Model**: Max Concurrent Users = (Total RAM - 3.2GB) / 640MB per user
- **Resource Allocation**:
  - **Core Platform**: 3.2GB (CTFd 450MB, Database 450MB, Cache 450MB, Proxy 450MB, Monitoring 896MB, FRP 500MB)
  - **Per User**: 640MB (Web desktop 512MB + Challenge container 128MB)
- **Deployment Capacity Examples**:
  - 8GB RAM = 7 users (Small classroom/lab)
  - 16GB RAM = 20 users (Standard classroom)
  - 32GB RAM = 45 users (Large classroom/workshop)
  - 64GB RAM = 95 users (Competition/conference)
- **Linear Scalability**: Predictable growth enabling precise planning for educational deployments

#### **Slide 26: Network Security and Isolation**
- **Multi-layer Network Architecture**:
  - **Overlay Networks**: FRP Network (172.1.0.0/16), Container Network (172.2.0.0/16)
  - **Multi-tier Protection**: Nginx rate limiting (25 req/min), application dynamic thresholds
  - **Communication Encryption**: Mandatory TLS, automatic certificates, security headers
- **User Isolation**:
  - **User-specific Networks**: Complete isolation with automatic creation/destruction
  - **Automatic Cleanup**: Session timeout (1 hour), limited renewal (5 max), resource recovery
- **Enterprise Security with Educational Flexibility**

---

### **4️⃣ SECTION 4: IMPLEMENTATION AND TECHNICAL REALIZATION (Slides 27-35)**

#### **Slide 27: Section Divider**
- Section 4 header with updated progress navigation

#### **Slide 28: Development Methodology**
- **Structured Lifecycle Approach**:
  - **Phase 1**: CTFd Foundation Configuration (base framework, Docker Swarm, overlay networks)
  - **Phase 2**: ctfd-whale Integration (enhanced plugin, FRP architecture, dynamic orchestration)
  - **Phase 3**: Web Desktop Plugin Development (database models, Flask routes, VNC/noVNC integration)
  - **Phase 4**: Security Monitoring (monitoring plugin, real-time dashboards, alert system)
- **Continuous Quality Assurance**: Unit testing, integration testing, system testing, security testing

#### **Slide 29: Key Technical Achievements**
- **Implemented Innovations**:
  - **Enhanced ctfd-whale Migration**: Improved orchestration, optimized container lifecycle, robust FRP integration
  - **Web Desktop Implementation**: Browser-based tool access, pre-configured Kali Linux, administrative templates
  - **Real-time Security Monitoring**: Threat detection with educational privacy, platform dashboards, automatic alerts
  - **Challenge Tracking Application**: Modern React/FastAPI interface, framework integration, educational analytics
  - **Complete Ecosystem Integration**: Plugin ecosystem working harmoniously with operational visibility

#### **Slide 30: Plugin Architecture Implementation**
- **CTFd Integration Pattern**: Plugin initialization following CTFd framework standards
  - Register plugin assets and blueprints
  - Database schema extension with app context
  - Flask Blueprint registration
  - Administrative interface integration
- **Deep Flask Integration**:
  - **Application Context**: Complete access to CTFd services
  - **Database Extension**: SQLAlchemy models with CTFd relationships
  - **Blueprint Organization**: Modular routes and cohesive navigation
  - **Lifecycle Hooks**: Request observation without interference

#### **Slide 31: Container Management and Deployment**
- **Dynamic Deployment**:
  - **One-click Container Instantiation**: Create desktop containers using whale infrastructure
  - **Resource Configuration**: Configurable memory/CPU allocation, protection limits, standardized templates
  - **Lifecycle Management**: Automatic renewal with limits, resource cleanup, state recovery
- **Container Configuration**: User ID, Docker image, memory/CPU limits, container type, port mapping
- **Integration**: Delegates to proven whale container management system

#### **Slide 32: Web Desktop Demonstration**
- **Interface Features**:
  - **Container Launch Interface**: Desktop template selection, session configuration, one-click deployment
  - **Administrative Interface**: Template management, container monitoring, access controls
  - **User Desktop Environment**: Complete Kali Linux, browser access, persistent sessions
- **Innovation Features**:
  - **Installation Barrier Elimination**: Immediate access to professional tools, standardized configuration
  - **Optimized User Experience**: Intuitive interface, acceptable performance, transparent CTFd integration

#### **Slide 33: Security Monitoring Dashboard**
- **Monitoring Features**:
  - **Main Security Dashboard**: Real-time metrics, security event visualization, resource usage statistics
  - **Alert and Incident Management**: Automatic severity classification, threat notification, incident history
  - **Monitoring Stack Integration**: Prometheus metrics, Grafana dashboards, Loki logs
- **Monitoring Capabilities**:
  - **Real-time Threat Detection**: Request pattern analysis, dynamic thresholds, exploitation distinction
  - **Operational Visibility**: Platform monitoring without educational interference, usage analytics, administrative reports

#### **Slide 34: Implemented Challenge Portfolio**
- **7 Developed and Deployed Challenges**:
  - SQLi Fashion Store (PHP/MariaDB, SQL Injection, CVSS 7.5)
  - ProfilePic Backdoor (PHP/HTML, File Upload, CVSS 8.8)
  - FileInjectX (PHP/HTML, Remote File Inclusion, CVSS 9.8)
  - SecureDoc Breakout (PHP/HTML, Path Traversal, CVSS 7.5)
  - SSRF Two-Part Flag (Node.js + React, SSRF, CVSS 7.5)
  - UpdateMe SQLi (PHP/MySQL, Second-Order SQLi, CVSS 8.8)
  - BookReviewz XSS (Node.js + React, XSS, CVSS 6.1)
- **Security Framework Coverage**: OWASP Top 10 (5/10 categories), MITRE ATT&CK (7 techniques), CWE (6 types), CVSS (6.1-9.8)
- **Technology Diversity**: PHP/HTML Stack (5 challenges), Node.js + React 18 (2 challenges), CVE-based vulnerabilities

#### **Slide 35: Performance and Security Validation**
- **Validated Performance Metrics**:
  - **Container Startup Times**: Challenges <30s, Desktop environments <60s, Interface response <2s
  - **Concurrent User Capacity**: 95 simultaneous users on 64GB system, linear scalability validated
  - **Stability**: Extended load testing without degradation
- **Security Testing Performed**:
  - **Isolation Verification**: Penetration testing, network validation, configuration audit
  - **Monitoring Validation**: Threat detection testing, false positive verification, performance impact assessment
- **Educational Validation**: User testing, instructor feedback, objective alignment validation

---

### **5️⃣ SECTION 5: RESULTS AND EDUCATIONAL IMPACT (Slides 36-38)**

#### **Slide 36: Section Divider**
- Section 5 header with updated progress navigation

#### **Slide 37: Platform Capabilities Summary**
- **Major Technical Achievements**:
  - **Validated Scalability**: Mathematical formula, 95 simultaneous users on 64GB, resource predictability
  - **Universal Accessibility**: Browser access, standardized Kali Linux, modern browser compatibility
  - **Complete Educational Integration**: Security framework alignment, structured progression, certification preparation
  - **Architecture Innovation**: Plugin system, container orchestration, intelligent monitoring
- **DATAPROTECT Deployment**: Successful integration into training initiatives, support for diverse client programs (10-100+ participants)

#### **Slide 38: Educational Impact and Industry Relevance**
- **Workforce Development Contribution**:
  - **Addresses Global Shortage**: 4.8 million missing professionals, large-scale accessible training, multiple institutional deployments
  - **Practical Skills Development**: Hands-on experience, theory-practice gap elimination, professional environment preparation
- **Industry Standards Alignment**:
  - **Certification Preparation**: Integrated frameworks, professional standards alignment, structured progression
  - **Scalable Enterprise Training**: Deployment flexibility (10-100+ participants), customization, reduced training costs
- **Open Source Potential**: CTFd enhancement, community adoption, continuous innovation foundation
- **Lasting Impact**: Cybersecurity education innovation with global reach

---

### **6️⃣ SECTION 6: CONCLUSION AND FUTURE PERSPECTIVES (Slides 39-42)**

#### **Slide 39: Section Divider**
- Section 6 header with completed progress navigation

#### **Slide 40: Project Achievements**
- **Validated Technical Success**:
  - **Technical Success**: Functional platform addressing limitations, robust architecture, 95 users capacity validation
  - **Innovation Delivery**: Advanced containerization, installation barrier elimination, intelligent monitoring
  - **Educational Impact**: Practical workforce development solution, theory-practice bridge, educational scalability
  - **Framework Integration**: Systematic industry standards alignment, professional certification preparation
- **Response to Initial Problem Statement**: How to solve the shortage of 4.8 million cybersecurity professionals?
- **PENQUEST demonstrates**: Containerization + Frameworks + Monitoring = Accessible and scalable cybersecurity training

#### **Slide 41: Future Perspectives**
- **Technical Improvements**:
  - **Advanced Analytics**: Machine learning for personalized paths, user behavior analysis, learning difficulty prediction
  - **Collaborative Features**: Shared environments, real-time SOC simulation, team competitions
  - **AI Integration**: Intelligent tutoring, automatic challenge generation, virtual assistant support
- **Domain Extensions**:
  - **Extended Frameworks**: Cloud security (AWS, Azure, GCP), IoT security, AI security challenges
  - **Community Adoption**: Open-source contribution, institutional adoption, certification integration
- **Vision**: PENQUEST as global standard for practical cybersecurity training

#### **Slide 42: Thank You**
- **Thank You for Your Attention!**
- **PENQUEST**: Advanced Cybersecurity Training Platform
- **Impact**: Contributing to solving the 4.8M cybersecurity professional shortage
- **Questions & Discussion**:
  - **Author**: Ilyas BAJJI
  - **Email**: <EMAIL>
  - **Partner**: DATAPROTECT
- **Project Resources**: Complete technical report, DATAPROTECT integration, future open source contribution
- **Key Achievements**: 95 Concurrent Users, 7 Implemented Challenges, 4 Integrated Security Frameworks

---

## 🎯 **PRESENTATION DELIVERY STRATEGY**

### **Time Allocation (20 minutes)**
- **Section 1**: 5 minutes (Global context + DATAPROTECT)
- **Section 2**: 3 minutes (Technology analysis)
- **Section 3**: 5 minutes (Architecture + Design)
- **Section 4**: 5 minutes (Implementation + Results)
- **Section 5-6**: 2 minutes (Impact + Conclusion)

### **Key Metrics to Emphasize**
- **4.8 million**: Global cybersecurity workforce shortage
- **95 users**: Validated concurrent capacity
- **640MB per user**: Resource scalability formula
- **7 challenges**: Complete security framework integration

### **Demonstration Elements**
- **Live Platform Navigation**: CTFd interface with challenges
- **Container Deployment**: Dynamic challenge instantiation
- **Web Desktop Access**: Browser-based tool demonstration
- **Monitoring Dashboard**: Real-time security visualization

### **Technical Depth Balance**
- **Academic Rigor**: Systematic methodology and validation
- **Innovation Highlight**: Plugin architecture and containerization
- **Practical Impact**: Working platform addressing real needs
- **Industry Relevance**: DATAPROTECT integration and standards alignment

---

## 📋 **PREPARATION CHECKLIST**

### **Content Preparation**
- [ ] Review all slides for message consistency
- [ ] Prepare live technical demonstrations
- [ ] Test navigation and slide transitions
- [ ] Validate metrics and presented data

### **Question Preparation**
- [ ] Commercial CTF platform comparison
- [ ] Containerized environment security model
- [ ] Scalability beyond tested capacity
- [ ] Educational effectiveness vs. traditional methods
- [ ] Integration with existing cybersecurity curricula
- [ ] Deployment and maintenance requirements

### **Technical Setup**
- [ ] Test presentation on final equipment
- [ ] PDF backup in case of technical issues
- [ ] Internet connection verification for demos
- [ ] Backup screenshots if live demo fails

This guide positions PENQUEST as a significant technical achievement and meaningful contribution to cybersecurity education, demonstrating competency for the State Engineer degree while showcasing practical industry impact.
