/* Section Header Styling */
.section-header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 60vh;
  text-align: center;
}

.section-header h1 {
  font-size: 6rem;
  font-weight: 900;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 600;
  color: white;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  max-width: 90%;
  line-height: 1.2;
}

/* Progress Navigation Styling */
.progress-nav {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  padding: 0.75rem 1.25rem;
  border-radius: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 100;
  max-width: 80vw;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.nav-item {
  padding: 0.5rem 1rem;
  border-radius: 1.25rem;
  font-size: 0.85rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  cursor: pointer;
  white-space: nowrap;
  text-align: center;
  border: 1px solid transparent;
}

.nav-item.completed {
  background: rgba(34, 197, 94, 0.8);
  color: white;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.nav-item.active {
  background: rgba(59, 130, 246, 0.9);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .section-header h1 {
    font-size: 4rem;
  }
  
  .section-header h2 {
    font-size: 1.75rem;
  }
  
  .progress-nav {
    padding: 0.5rem 0.75rem;
    gap: 0.125rem;
    width: 95vw;
  }
  
  .nav-item {
    padding: 0.375rem 0.5rem;
    font-size: 0.7rem;
    min-width: 0;
  }
}

@media (max-width: 480px) {
  .nav-item {
    font-size: 0.65rem;
    padding: 0.25rem 0.375rem;
  }
  
  .progress-nav {
    width: 98vw;
    padding: 0.375rem 0.5rem;
  }
}/* 🎨 PFE PRESENTATION STYLING TEMPLATE */

/* ================================
   ENHANCED PRESENTATION ROADMAP
   ================================ */

.presentation-roadmap {
    margin: 20px 0;
    padding: 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.1);
}

.roadmap-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #dee2e6;
}

.roadmap-header h3 {
    margin: 0 0 15px 0;
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
}

.presentation-meta {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.presentation-meta span {
    background: #ffffff;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.toc-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.toc-section {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.toc-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    border-color: #007bff;
}

.section-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 15px;
}

.section-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    color: #495057;
    border: 2px solid #dee2e6;
    border-radius: 50%;
    font-weight: 700;
    font-size: 16px;
    flex-shrink: 0;
}

.section-info {
    flex-grow: 1;
}

.section-info h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.3;
}

.section-desc {
    margin: 0 0 10px 0;
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
}

.section-meta {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.slides-count {
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: 600;
}

.time-estimate {
    background: #d4edda;
    color: #155724;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: 600;
}

.section-highlights {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px;
}

.highlight {
    background: #e3f2fd;
    color: #1565c0;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid #bbdefb;
}

.presentation-flow {
    margin: 30px 0;
    padding: 20px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.flow-title {
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
}

.flow-stages {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.flow-stage {
    background: #f8f9fa;
    color: #495057;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid #dee2e6;
    white-space: nowrap;
}

.flow-arrow {
    font-size: 16px;
    color: #6c757d;
    font-weight: bold;
}

.key-achievements {
    margin-top: 30px;
    padding: 20px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.key-achievements h3 {
    text-align: center;
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.achievement {
    text-align: center;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.achievement .metric {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: #007bff;
    margin-bottom: 5px;
}

.achievement .label {
    font-size: 11px;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ================================
   LEGACY TOC STYLES (keep for compatibility)
   ================================ */

.toc-container {
    margin: 20px 0;
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.toc-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.toc-item:last-child {
    border-bottom: none;
}

.toc-item:hover {
    background: rgba(0,123,255,0.05);
    border-radius: 6px;
    padding-left: 8px;
    padding-right: 8px;
    transform: translateX(4px);
}

.toc-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: #f8f9fa;
    color: #495057;
    border: 2px solid #dee2e6;
    border-radius: 50%;
    font-weight: 700;
    font-size: 14px;
    margin-right: 16px;
    flex-shrink: 0;
}

.toc-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    flex-grow: 1;
    margin-right: 12px;
}

.toc-dots {
    flex-grow: 1;
    border-bottom: 2px dotted #adb5bd;
    margin: 0 8px;
    height: 1px;
}

.toc-slides {
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 12px;
    min-width: 40px;
    text-align: center;
}

.toc-summary {
    margin-top: 25px;
    padding: 20px;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 8px;
    border-left: 4px solid #2196f3;
}

.toc-summary h3 {
    margin: 0 0 12px 0;
    color: #1565c0;
    font-size: 18px;
    font-weight: 600;
}

.toc-summary ul {
    margin: 0;
    padding-left: 20px;
}

.toc-summary li {
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.5;
}

.toc-summary strong {
    color: #1565c0;
    font-weight: 600;
}

/* ================================
   PROGRESS TRACKING STYLES
   ================================ */

/* Navigation breadcrumb */
.section-nav {
    display: flex;
    justify-content: center;
    margin: 10px 0;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.nav-item {
    padding: 8px 16px;
    margin: 0 4px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

/* Navigation states */
.nav-item.completed {
    background: #28a745;  /* Green */
    color: white;
}

.nav-item.current {
    background: #007bff;  /* Blue */
    color: white;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}

.nav-item.pending {
    background: #e9ecef;  /* Gray */
    color: #6c757d;
}

/* ================================
   SECTION HEADERS
   ================================ */

.section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    position: relative;
}

.section-number {
    display: inline-block;
    background: rgba(255,255,255,0.2);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 18px;
    font-weight: bold;
    margin-right: 15px;
}

.section-title {
    display: inline-block;
    font-size: 24px;
    font-weight: 600;
}

/* ================================
   SUBSECTION TRACKING
   ================================ */

.subsection-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 15px 0;
    padding: 15px;
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
}

.subsection-item {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.subsection-item.completed::before {
    content: "✅";
}

.subsection-item.current {
    background: #cce5ff;
    border: 2px solid #007bff;
}

.subsection-item.current::before {
    content: "🔵";
}

.subsection-item.pending::before {
    content: "⚪";
}

/* ================================
   PROGRESS INDICATORS
   ================================ */

.progress-container {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
    border-radius: 10px;
    transition: width 0.5s ease;
}

.progress-text {
    text-align: center;
    font-weight: 600;
    margin-top: 5px;
}

/* ================================
   CONTENT STYLING
   ================================ */

/* Problem highlighting */
.problem-highlight {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
}

/* Solution highlighting */
.solution-highlight {
    background: #d1edff;
    border-left: 4px solid #007bff;
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
}

/* Results highlighting */
.results-highlight {
    background: #d4edda;
    border-left: 4px solid #28a745;
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
}

/* Key metrics */
.metric-box {
    display: inline-block;
    background: #007bff;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: bold;
    margin: 5px;
    text-align: center;
}

.metric-value {
    font-size: 24px;
    display: block;
}

.metric-label {
    font-size: 12px;
    opacity: 0.9;
}

/* ================================
   RESPONSIVE DESIGN
   ================================ */

/* ================================
   RESPONSIVE DESIGN ENHANCEMENTS
   ================================ */

@media (max-width: 1024px) {
    .toc-grid {
        grid-template-columns: 1fr;
    }
    
    .presentation-meta {
        gap: 15px;
    }
    
    .presentation-meta span {
        font-size: 12px;
        padding: 6px 12px;
    }
}

@media (max-width: 768px) {
    .section-nav {
        flex-direction: column;
        gap: 5px;
    }
    
    .nav-item {
        text-align: center;
    }
    
    .subsection-nav {
        flex-direction: column;
    }
    
    .toc-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .toc-dots {
        display: none;
    }
    
    .toc-title {
        margin-right: 0;
    }
    
    .presentation-roadmap {
        padding: 20px;
    }
    
    .toc-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .section-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 10px;
    }
    
    .flow-stages {
        flex-direction: column;
        gap: 10px;
    }
    
    .flow-arrow {
        transform: rotate(90deg);
    }
    
    .achievements-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .presentation-meta {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
    
    .achievements-grid {
        grid-template-columns: 1fr;
    }
    
    .achievement .metric {
        font-size: 18px;
    }
    
    .achievement .label {
        font-size: 10px;
    }
}

/* ================================
   ENHANCED ANIMATION EFFECTS
   ================================ */

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    70% {
        transform: scale(0.9);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Apply animations to elements */
.presentation-roadmap {
    animation: fadeInScale 0.8s ease-out;
}

.toc-section {
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.toc-section:nth-child(1) { animation-delay: 0.1s; }
.toc-section:nth-child(2) { animation-delay: 0.2s; }
.toc-section:nth-child(3) { animation-delay: 0.3s; }
.toc-section:nth-child(4) { animation-delay: 0.4s; }
.toc-section:nth-child(5) { animation-delay: 0.5s; }
.toc-section:nth-child(6) { animation-delay: 0.6s; }

.flow-stage {
    animation: slideInLeft 0.5s ease-out;
    animation-fill-mode: both;
}

.flow-stage:nth-child(odd) { animation-delay: 0.1s; }
.flow-stage:nth-child(even) { animation-delay: 0.2s; }

.achievement {
    animation: bounceIn 0.6s ease-out;
    animation-fill-mode: both;
}

.achievement:nth-child(1) { animation-delay: 0.1s; }
.achievement:nth-child(2) { animation-delay: 0.2s; }
.achievement:nth-child(3) { animation-delay: 0.3s; }
.achievement:nth-child(4) { animation-delay: 0.4s; }

.current {
    animation: pulse 2s infinite;
}

.slide-in {
    animation: slideInLeft 0.5s ease-out;
}

/* Hover animations */
.toc-section:hover .section-number {
    transform: scale(1.1);
    background: #e3f2fd;
    border-color: #2196f3;
    color: #1565c0;
}

.achievement:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,123,255,0.15);
}

.achievement:hover .metric {
    transform: scale(1.1);
    color: #0056b3;
}

/* Presentation meta hover effects */
.presentation-meta span:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    background: #e3f2fd;
    color: #1565c0;
}

/* Flow stages hover effect */
.flow-stage:hover {
    background: #e3f2fd;
    color: #1565c0;
    transform: scale(1.05);
}

/* ================================
   ACCESSIBILITY ENHANCEMENTS
   ================================ */

@media (prefers-reduced-motion: reduce) {
    .toc-section,
    .flow-stage,
    .achievement,
    .presentation-roadmap {
        animation: none;
    }
    
    .toc-section:hover,
    .achievement:hover,
    .flow-stage:hover {
        transform: none;
    }
}

/* ================================
   PRINT STYLES
   ================================ */

@media print {
    .section-nav,
    .progress-container {
        display: none;
    }
    
    .section-header {
        background: #333 !important;
        color: white !important;
    }
}
