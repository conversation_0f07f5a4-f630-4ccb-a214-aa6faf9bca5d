    cursor: pointer;
    user-select: none;
}

.nav-item.completed {
    background: var(--color-success);
    color: white;
}

.nav-item.current {
    background: var(--color-secondary);
    color: white;
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.nav-item.pending {
    background: #e9ecef;
    color: var(--color-muted);
}

/* ================================
   CONTENT TYPOGRAPHY
   ================================ */

.slide-title {
    font-size: var(--fs-xl);
    font-weight: var(--fw-semibold);
    color: var(--color-primary);
    border-bottom: 3px solid var(--color-secondary);
    padding-bottom: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.content-text {
    font-size: var(--fs-base);
    font-weight: var(--fw-normal);
    line-height: var(--lh-relaxed);
    margin-bottom: var(--spacing-md);
}

.bullet-text {
    font-size: var(--fs-sm);
    font-weight: var(--fw-normal);
    line-height: 1.7;
}

.caption-text {
    font-size: var(--fs-xs);
    font-style: italic;
    color: var(--color-muted);
    text-align: center;
    margin-top: var(--spacing-sm);
}

/* ================================
   METRIC BOXES & SPECIAL ELEMENTS
   ================================ */

.metric-box {
    display: inline-block;
    background: var(--color-secondary);
    color: white;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    text-align: center;
    margin: var(--spacing-sm);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.metric-value {
    font-size: var(--fs-xxl);
    font-weight: var(--fw-extrabold);
    display: block;
    line-height: var(--lh-tight);
}

.metric-label {
    font-size: var(--fs-xxs);
    font-weight: var(--fw-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.9;
}

/* Code elements */
code, .code-text {
    font-family: var(--code-font);
    font-size: var(--fs-xs);
    background: #f8f9fa;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    border: 1px solid #e9ecef;
}

/* ================================
   TABLE TYPOGRAPHY
   ================================ */

table {
    width: 100%;
    border-collapse: collapse;
    margin: var(--spacing-lg) 0;
    background: white;
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

th {
    font-size: var(--fs-sm);
    font-weight: var(--fw-semibold);
    color: white;
    background: var(--color-secondary);
    padding: var(--spacing-md);
    text-align: left;
}

td {
    font-size: var(--fs-xs);
    font-weight: var(--fw-normal);
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid #e9ecef;
}

/* ================================
   PROGRESS TRACKING
   ================================ */

.progress-container {
    margin: var(--spacing-lg) 0;
    padding: var(--spacing-md);
    background: var(--color-light);
    border-radius: var(--radius-md);
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin: var(--spacing-sm) 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--color-success) 0%, #20c997 100%);
    border-radius: var(--radius-lg);
    transition: width 0.5s ease;
}

.progress-text {
    text-align: center;
    font-size: var(--fs-xs);
    font-weight: var(--fw-semibold);
    margin-top: var(--spacing-sm);
}

/* ================================
   CONTENT HIGHLIGHTING
   ================================ */

.problem-highlight {
    background: rgba(243, 156, 18, 0.1);
    border-left: 4px solid var(--color-warning);
    padding: var(--spacing-md);
    margin: var(--spacing-md) 0;
    border-radius: var(--radius-sm);
}

.solution-highlight {
    background: rgba(52, 152, 219, 0.1);
    border-left: 4px solid var(--color-secondary);
    padding: var(--spacing-md);
    margin: var(--spacing-md) 0;
    border-radius: var(--radius-sm);
}

.results-highlight {
    background: rgba(39, 174, 96, 0.1);
    border-left: 4px solid var(--color-success);
    padding: var(--spacing-md);
    margin: var(--spacing-md) 0;
    border-radius: var(--radius-sm);
}

/* ================================
   RESPONSIVE DESIGN
   ================================ */

@media (max-width: 768px) {
    :root {
        --fs-mega: 28px;
        --fs-xxl: 24px;
        --fs-xl: 22px;
        --fs-lg: 18px;
        --fs-md: 16px;
        --fs-base: 14px;
        --fs-sm: 12px;
        --fs-xs: 11px;
        --fs-xxs: 10px;
    }

    .title-slide {
        padding: var(--spacing-md);
    }

    .section-nav {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .nav-item {
        text-align: center;
    }
}

@media (max-width: 480px) {
    :root {
        --fs-mega: 24px;
        --fs-xxl: 20px;
        --fs-xl: 18px;
        --fs-lg: 16px;
        --fs-md: 14px;
        --fs-base: 12px;
        --fs-sm: 11px;
        --fs-xs: 10px;
        --fs-xxs: 9px;
    }
}

/* ================================
   ANIMATIONS
   ================================ */

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

.current {
    animation: pulse 2s infinite;
}

/* ================================
   PRINT STYLES
   ================================ */

@media print {
    .section-nav,
    .progress-container {
        display: none;
    }
    
    .section-header {
        background: #333 !important;
        color: white !important;
    }
    
    body {
        font-size: 12px;
    }
    
    .title-slide .main-title {
        font-size: 24px;
    }
}

/* ================================
   UTILITY CLASSES
   ================================ */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.fw-bold { font-weight: var(--fw-bold); }
.fw-semibold { font-weight: var(--fw-semibold); }
.fw-medium { font-weight: var(--fw-medium); }
.fw-normal { font-weight: var(--fw-normal); }

.fs-mega { font-size: var(--fs-mega); }
.fs-xxl { font-size: var(--fs-xxl); }
.fs-xl { font-size: var(--fs-xl); }
.fs-lg { font-size: var(--fs-lg); }
.fs-md { font-size: var(--fs-md); }
.fs-base { font-size: var(--fs-base); }
.fs-sm { font-size: var(--fs-sm); }
.fs-xs { font-size: var(--fs-xs); }
.fs-xxs { font-size: var(--fs-xxs); }

.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-muted { color: var(--color-muted); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.d-flex { display: flex; }
.d-block { display: block; }
.d-inline-block { display: inline-block; }
.justify-content-center { justify-content: center; }
.align-items-center { align-items: center; }

.bg-light { background-color: var(--color-light); }
.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }

.rounded { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/*
🎓 PFE PRESENTATION COMPLETE TYPOGRAPHY SYSTEM

This stylesheet provides:
✅ Professional font hierarchy
✅ Complete title slide styling
✅ Progress tracking system
✅ Responsive typography
✅ Content highlighting
✅ Animation effects
✅ Print optimization
✅ Utility classes

Perfect for academic presentations following
professional standards used in the PDF example.
*/
