# PDF to PNG Converter for PENQUEST Presentation

## Quick Start

### Option 1: Double-click to run (Windows)
1. Double-click `run_converter.bat`
2. Follow the prompts

### Option 2: Command line
```bash
cd C:\Users\<USER>\Desktop\ppt\public
python convert_pdfs_to_png.py
```

## What it does

✅ **Finds all PDFs** in `figures/diagrams-gant-and-uml/`  
✅ **Converts to high-quality PNG** (180+ DPI)  
✅ **Saves in same directory** as original PDF  
✅ **Skips existing PNG files** (won't overwrite)  
✅ **Shows detailed progress** and summary  

## Requirements

The script will automatically install these if missing:
- `PyMuPDF` (for PDF processing)
- `Pillow` (for image handling)

## Example Output

```
diagrams-gant-and-uml/
├── images-related-to-try1-ctfd-to-whale/
│   ├── CTFd-Whale Architecture.pdf
│   ├── CTFd-Whale Architecture.png ← NEW!
│   └── ...
├── ctfd plugin/
│   ├── CTFd-Plugin-Architecture.pdf
│   ├── CTFd-Plugin-Architecture.png ← NEW!
│   └── ...
```

## After Conversion

Update your `slides.md` files to use PNG instead of PDF:

**Before:**
```html
<img src="/figures/.../diagram.pdf" alt="...">
```

**After:**
```html
<img src="/figures/.../diagram.png" alt="...">
```

## Troubleshooting

**"No module named 'fitz'"**
- Run: `pip install PyMuPDF`

**"Permission denied"**
- Close any PDF viewers
- Run as administrator

**"Directory not found"**
- Make sure you're running from `C:\Users\<USER>\Desktop\ppt\public\`

## Why Convert?

- ✅ **PNG works in HTML img tags** (PDF doesn't)
- ✅ **Faster loading** in web browsers
- ✅ **Universal compatibility** across all devices
- ✅ **Better for presentations** in Slidev

---

💡 **Created for PENQUEST presentation by Claude AI**
