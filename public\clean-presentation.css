/* PENQUEST Presentation - Clean Styling */

/* ===== NAVIGATION TABS ===== */
.nav-tabs {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin: 1.5rem 0;
  flex-wrap: wrap;
  padding: 0 1rem;
}

.nav-tab {
  background: #f8f9fa;
  color: #6b7280;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
  cursor: default;
  white-space: nowrap;
}

.nav-tab.active {
  background: #3b82f6;
  color: white;
  font-weight: 600;
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

/* ===== TYPOGRAPHY FIXES ===== */
.content-slide h3 {
  font-size: 1.25rem !important;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #374151;
  line-height: 1.4;
}

.content-slide h4 {
  font-size: 1.1rem !important;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #4b5563;
}

/* Remove excessive styling from headers */
.content-slide h3[style] {
  font-size: 1.25rem !important;
}

/* ===== LAYOUT COMPONENTS ===== */
.two-column {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 1.5rem 0;
  align-items: start;
}

.three-column {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1.5rem;
  margin: 1.5rem 0;
}

.single-column {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* ===== FIGURE STYLING ===== */
.figure-container {
  text-align: center;
  margin: 1rem 0;
}

.figure-container img {
  max-height: 400px;
  max-width: 100%;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  object-fit: contain;
}

.figure-small img {
  max-height: 250px;
}

.figure-large img {
  max-height: 500px;
}

/* ===== CONTENT CARDS ===== */
.content-card {
  background: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

.metric-card {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border: 1px solid #0ea5e9;
  border-radius: 0.5rem;
  padding: 1rem;
  text-align: center;
  margin: 0.5rem;
}

.metric-card h4 {
  color: #0369a1;
  font-size: 1.5rem !important;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.metric-card p {
  color: #0c4a6e;
  font-size: 0.875rem;
  margin: 0;
}

/* ===== LISTS AND TABLES ===== */
.content-slide ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.content-slide li {
  margin: 0.5rem 0;
  line-height: 1.4;
}

.simple-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  font-size: 0.9rem;
}

.simple-table th,
.simple-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.simple-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

/* ===== PROGRESS INDICATORS ===== */
.section-progress {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin: 1rem 0;
}

.progress-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #d1d5db;
  transition: all 0.2s ease;
}

.progress-dot.active {
  background: #3b82f6;
  transform: scale(1.2);
}

.progress-dot.completed {
  background: #10b981;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .nav-tabs {
    gap: 0.25rem;
  }
  
  .nav-tab {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .two-column {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .three-column {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* ===== SECTION DIVIDERS ===== */
.section-divider {
  height: 2px;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  margin: 2rem 0;
  border: none;
}

/* ===== UTILITY CLASSES ===== */
.text-center {
  text-align: center;
}

.text-small {
  font-size: 0.875rem;
}

.text-large {
  font-size: 1.125rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-4 {
  margin-top: 1rem;
}

.p-4 {
  padding: 1rem;
}

/* ===== ANIMATION FIXES ===== */
* {
  box-sizing: border-box;
}

.content-slide {
  animation: none !important;
  transition: none !important;
}

/* Override any problematic styles from the original */
[style*="background-color"][style*="padding"][style*="border-radius"] {
  all: unset !important;
  display: inline-block !important;
}

/* Clean up inline styles that cause errors */
span[style*="background-color: #3b82f6"] {
  background: #3b82f6 !important;
  color: white !important;
  padding: 0.375rem 0.75rem !important;
  border-radius: 0.375rem !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  display: inline-block !important;
  margin: 0.125rem !important;
}

span[style*="background-color: #9ca3af"] {
  background: #9ca3af !important;
  color: white !important;
  padding: 0.375rem 0.75rem !important;
  border-radius: 0.375rem !important;
  font-size: 0.875rem !important;
  display: inline-block !important;
  margin: 0.125rem !important;
}

span[style*="background-color: #10b981"] {
  background: #10b981 !important;
  color: white !important;
  padding: 0.375rem 0.75rem !important;
  border-radius: 0.375rem !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  display: inline-block !important;
  margin: 0.125rem !important;
}
