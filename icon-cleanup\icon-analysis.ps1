# PENQUEST Section 6 Icon Cleanup Script
# Preserves only icons related to Section 6 concepts:
# 1. Key Achievements (🛡️ Security/Achievement tools)
# 2. Impact & Validation (📊 Analytics/Monitoring tools) 
# 3. AI & Analytics Integration (🤖 AI/Development tools)
# 4. Platform Evolution (🌐 Platform/Web tools)

Write-Host "🎯 PENQUEST Section 6 Icon Cleanup Analysis" -ForegroundColor Cyan
Write-Host "=" * 50

# Section 6 concepts to preserve
$Section6Concepts = @{
    "Key Achievements" = @("security", "achievement", "shield", "protection", "antivirus", "defender")
    "Impact & Validation" = @("analytics", "chart", "graph", "monitor", "dashboard", "metrics", "performance")
    "AI & Analytics Integration" = @("ai", "robot", "development", "code", "vscode", "python", "docker", "git")
    "Platform Evolution" = @("web", "browser", "platform", "cloud", "internet", "global", "network")
}

# Get desktop icons
Write-Host "📋 Analyzing Desktop Icons..." -ForegroundColor Yellow
$DesktopPath = [Environment]::GetFolderPath("Desktop")
$DesktopIcons = Get-ChildItem -Path $DesktopPath -Filter "*.lnk"

Write-Host "🔍 Found $($DesktopIcons.Count) desktop shortcuts"

# Analyze each icon
$PreserveIcons = @()
$RemoveIcons = @()

foreach ($icon in $DesktopIcons) {
    $iconName = $icon.BaseName.ToLower()
    $shouldPreserve = $false
    $matchedConcept = ""
    
    foreach ($concept in $Section6Concepts.Keys) {
        foreach ($keyword in $Section6Concepts[$concept]) {
            if ($iconName -like "*$keyword*") {
                $shouldPreserve = $true
                $matchedConcept = $concept
                break
            }
        }
        if ($shouldPreserve) { break }
    }
    
    if ($shouldPreserve) {
        $PreserveIcons += [PSCustomObject]@{
            Name = $icon.BaseName
            Path = $icon.FullName
            Concept = $matchedConcept
        }
    } else {
        $RemoveIcons += [PSCustomObject]@{
            Name = $icon.BaseName
            Path = $icon.FullName
        }
    }
}

# Display results
Write-Host "`n✅ ICONS TO PRESERVE (Section 6 Related):" -ForegroundColor Green
if ($PreserveIcons.Count -eq 0) {
    Write-Host "   No icons match Section 6 concepts" -ForegroundColor Gray
} else {
    foreach ($concept in $Section6Concepts.Keys) {
        $conceptIcons = $PreserveIcons | Where-Object { $_.Concept -eq $concept }
        if ($conceptIcons.Count -gt 0) {
            Write-Host "   📌 $concept" -ForegroundColor Cyan
            foreach ($icon in $conceptIcons) {
                Write-Host "      - $($icon.Name)" -ForegroundColor White
            }
        }
    }
}

Write-Host "`n❌ ICONS TO REMOVE:" -ForegroundColor Red
if ($RemoveIcons.Count -eq 0) {
    Write-Host "   All icons are related to Section 6 concepts!" -ForegroundColor Gray
} else {
    foreach ($icon in $RemoveIcons) {
        Write-Host "   - $($icon.Name)" -ForegroundColor White
    }
}

# Generate cleanup script
$CleanupScript = @"
# Generated Icon Cleanup Script - Run with caution!
# This script will move non-Section 6 icons to a backup folder

`$BackupPath = "C:\Users\<USER>\Desktop\ppt\icon-cleanup\removed-icons"
New-Item -ItemType Directory -Path `$BackupPath -Force

Write-Host "🔄 Moving non-Section 6 icons to backup folder..." -ForegroundColor Yellow

"@

foreach ($icon in $RemoveIcons) {
    $CleanupScript += "Move-Item -Path '$($icon.Path)' -Destination `$BackupPath -Force`n"
}

$CleanupScript += @"

Write-Host "✅ Cleanup complete! Icons moved to: `$BackupPath" -ForegroundColor Green
Write-Host "💡 You can restore any icons from the backup folder if needed." -ForegroundColor Cyan
"@

# Save cleanup script
$CleanupScript | Out-File -FilePath "C:\Users\<USER>\Desktop\ppt\icon-cleanup\execute-cleanup.ps1" -Encoding UTF8

Write-Host "`n📄 Analysis complete!" -ForegroundColor Cyan
Write-Host "📁 Generated cleanup script: execute-cleanup.ps1" -ForegroundColor Yellow
Write-Host "⚠️  Review the analysis above before running the cleanup script!" -ForegroundColor Red

# Summary
Write-Host "`n📊 SUMMARY:" -ForegroundColor Cyan
Write-Host "   Icons to preserve: $($PreserveIcons.Count)" -ForegroundColor Green
Write-Host "   Icons to remove: $($RemoveIcons.Count)" -ForegroundColor Red
Write-Host "   Total icons analyzed: $($DesktopIcons.Count)" -ForegroundColor White
