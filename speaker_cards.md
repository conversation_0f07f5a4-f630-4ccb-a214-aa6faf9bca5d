# PENQUEST Platform - Detailed Speaker Cards

## Slide 1: Title Slide
**Opening (30 seconds)**
"Good morning, distinguished committee members. I am <PERSON><PERSON>, and I'm honored to present my end-of-studies project defense for obtaining the State Engineer Diploma. Today, I will present PENQUEST - a cybersecurity challenge platform that I developed during my internship at DATAPROTECT, Morocco's leading cybersecurity company. This project represents six months of intensive work from February to June 2025, aimed at revolutionizing how we teach and learn cybersecurity through practical, hands-on experience."

## Slide 2: Presentation Outline
**Transition (20 seconds)**
"My presentation is structured into six comprehensive sections. We'll begin by understanding the context and global cybersecurity challenge, then explore the theoretical foundations and technology choices. We'll dive into the design and architecture, examine the technical implementation, analyze the results, and conclude with future perspectives. This progression will take you from problem identification to complete solution delivery."

## Slide 3: Section 1 Introduction
**Section Opening (10 seconds)**
"Let's begin with Section 1, where we'll explore the project context and the global cybersecurity challenge that motivated this work."

## Slide 4: DATAPROTECT Company Overview
**Company Introduction (45 seconds)**
"First, let me introduce DATAPROTECT, where this project was developed. DATAPROTECT is Morocco's premier cybersecurity company, truly living up to their motto 'Security is our commitment.' The company's impressive scale speaks volumes - over 220 cybersecurity professionals, more than 580 professional certifications, serving 450+ clients across 40 countries worldwide. This extensive expertise and global reach provided the perfect environment for developing a world-class cybersecurity education platform."

## Slide 5: Business Units
**Business Scope (30 seconds)**
"DATAPROTECT's comprehensive business units demonstrate their full-spectrum cybersecurity capabilities. From governance and risk management to technical security services, from incident response to security training, this diversity ensures that PENQUEST was developed with deep understanding of real-world cybersecurity needs across all domains."

## Slide 6: Client Portfolio
**Market Validation (25 seconds)**
"The company's impressive client portfolio spans critical sectors - banking, telecommunications, government, and industry. This real-world exposure to cybersecurity challenges across diverse environments directly informed the requirements and design decisions for the PENQUEST platform."

## Slide 7: Global Cybersecurity Challenge
**Problem Statement (60 seconds)**
"Now, let's examine the critical problem this project addresses. According to the 2024 (ISC)² Cybersecurity Workforce Study, we face a staggering global cybersecurity workforce gap of 4.8 million professionals - that's a 19% increase from 2023 alone. The total global need reaches 10.2 million cybersecurity professionals. This isn't just a number - it represents a fundamental threat to digital security worldwide. Traditional education methods are failing to bridge this gap effectively. CTF training emerges as a powerful solution to bridge the theory-practice gap that exists in conventional cybersecurity education."

## Slide 8: Problem Statement
**Educational Gap Analysis (50 seconds)**
"The fundamental problem lies in the stark contrast between traditional education and industry needs. Traditional cybersecurity education is heavily theory-focused, offers limited hands-on practice, lacks real-world scenarios, and relies on static assessments. In contrast, the PENQUEST solution provides interactive CTF challenges, containerized environments that simulate real systems, authentic vulnerability simulations, and comprehensive progress tracking. This represents a paradigm shift from passive learning to active, engaging cybersecurity education."

## Slide 9: Project Objectives
**Goal Setting (40 seconds)**
"PENQUEST addresses four critical objectives. Technical enhancement through advanced containerization and automation. Educational innovation via framework integration and progressive learning. Strategic impact by bridging academia and industry needs. And comprehensive monitoring and quality assurance throughout the learning process. These objectives ensure our solution addresses both immediate educational needs and long-term industry requirements."

## Slide 10: Project Timeline
**Development Schedule (25 seconds)**
"The project followed a structured timeline from February to June 2025, with clearly defined phases including analysis, design, implementation, and validation. This Gantt chart demonstrates the systematic approach taken to deliver a production-ready educational platform."

## Slide 11: Section 2 Introduction
**Section Transition (15 seconds)**
"Moving to Section 2, we'll explore the theoretical foundations and technology analysis that guided our design decisions."

## Slide 12: Platform Selection
**Technology Justification (45 seconds)**
"Platform selection was critical to project success. I conducted a comprehensive benchmarking analysis comparing CTFd, FBCTF, and RootTheBox across multiple criteria. CTFd emerged as the clear winner with a 9.2/10 overall rating, excelling in deployment ease, comprehensive assessment capabilities, and extensive plugin support. This evaluation was based on established research methodology from Karagiannis et al. 2020, ensuring our selection was scientifically rigorous."

## Slide 13: Flask Framework Foundation
**Technical Architecture (40 seconds)**
"CTFd's Flask foundation provides the architectural flexibility essential for educational platforms. The plugin architecture integrates seamlessly with the application context, supports blueprint registration, and enables request lifecycle hooks. This WSGI-compliant framework allows modular development and educational customization while maintaining the extensibility needed for specialized cybersecurity training features."

## Slide 14: Docker Security Fundamentals
**Containerization Benefits (45 seconds)**
"Docker containerization addresses critical educational requirements through three key mechanisms. Process isolation using Linux namespaces and cgroups ensures secure separation between user environments. Resource management prevents resource exhaustion attacks that could disrupt the learning experience. Container consistency guarantees identical environments across our entire infrastructure, eliminating the 'it works on my machine' problem that plagues traditional lab setups."

## Slide 15: Docker Swarm Orchestration
**Orchestration Choice (40 seconds)**
"Docker Swarm was selected over Kubernetes for several compelling reasons. The architecture includes manager nodes providing Raft consensus orchestration, worker nodes for task execution, services for desired state definition, and overlay networks for secure communication. Key advantages include low setup complexity, minimal learning curve, reduced resource overhead, and suitability for small-to-medium scale educational deployments."

## Slide 16: CTFd-whale Plugin & FRP
**Dynamic Environment Management (45 seconds)**
"The CTFd-whale plugin provides dynamic instance deployment with complete user isolation through Docker Swarm integration and administrative management capabilities. FRP integration enables secure tunneling through frps/frpc architecture, supporting HTTP, HTTPS, TCP, and UDP protocols with dynamic configuration and subdomain isolation. This combination delivers true per-user containerized challenge environments."

## Slide 17: OWASP Top 10
**Security Framework Integration (60 seconds)**
"The OWASP Top 10 provides our industry-standard web application security framework. From A01 Broken Access Control through A10 Server-Side Request Forgery, this framework covers the most critical web application security risks. Each category represents real-world vulnerabilities that cybersecurity professionals encounter daily. Our challenges are systematically mapped to these categories, ensuring comprehensive coverage of essential security concepts."

## Slide 18: MITRE ATT&CK Framework
**Attack Simulation Framework (35 seconds)**
"The MITRE ATT&CK framework structures our approach to attack simulation through tactics, techniques, and procedures. This globally recognized framework enables realistic attack scenario recreation, helping students understand both offensive and defensive cybersecurity perspectives."

## Slide 19: CWE & CVSS Classification
**Vulnerability Management (45 seconds)**
"CWE taxonomy provides systematic vulnerability classification from class-level categories down to specific variants like SQL Injection CWE-89. CVSS v3.1 scoring incorporates base metrics, temporal metrics, and environmental factors to provide comprehensive vulnerability prioritization. Together, these systems enable systematic vulnerability classification and prioritization throughout our educational content."

## Slide 20: React Frontend Framework
**Modern UI Development (35 seconds)**
"React's component-based architecture enables efficient UI development with virtual DOM optimization and effective state management. For educational applications, this translates to real-time challenge tracking, progress visualization, collaborative learning features, and responsive design support across all devices."

## Slide 21: Backend Framework Comparison
**API Development Strategy (40 seconds)**
"While CTFd uses Flask, our additional services leverage FastAPI for superior performance, automatic OpenAPI documentation, native type safety, and automatic validation. This complementary Python web development stack provides the best of both worlds - CTFd's educational foundation with modern API capabilities."

## Slide 22: Monitoring Stack
**Observability Architecture (40 seconds)**
"Our monitoring stack combines Prometheus for metrics collection and time-series database functionality, Grafana for data visualization and dashboard management, and Loki for log aggregation with privacy protection. This provides comprehensive platform observability while maintaining educational privacy standards."

## Slide 23: Section 3 Introduction
**Design Section Opening (15 seconds)**
"Section 3 explores the PENQUEST design and architecture, showing how theoretical foundations translate into practical system design."

## Slide 24: Architecture Foundation
**Framework Overview (30 seconds)**
"The CTFd-Whale plugin architecture framework demonstrates our modular extension approach, enabling sophisticated functionality without modifying CTFd's core codebase."

## Slide 25: Flask Integration Mechanisms
**Technical Integration (45 seconds)**
"Flask integration occurs through comprehensive application context access via the load() function, enabling database connections, authentication systems, and complete SQLAlchemy model integration. Extension points include Flask endpoints, blueprint registration, Jinja2 template overrides, and custom challenge type validation. This enables modular extension without any core CTFd modification."

## Slide 26: System Requirements
**Capacity Planning (50 seconds)**
"System requirements follow a strict resource management policy: one container per user per type, with memory allocation of 128MB for challenges and 512MB for desktop environments. Our capacity formula calculates maximum users as total memory minus 3.2GB divided by 640MB per user. Security includes network isolation using *********/16 for FRP and *********/16 for containers, rate limiting via Nginx, and container timeouts of 3600 seconds with maximum 5 renewals."

## Slide 27: Design Philosophy
**Architectural Principles (40 seconds)**
"Our design philosophy emphasizes three core principles. Separation of concerns ensures each plugin addresses specific functionality without interfering with core operations. Extensibility through composition allows multiple plugins to create sophisticated functionality through simple components. Educational continuity ensures platform enhancements preserve and enhance rather than complicate the learning experience."

## Slide 28: UML Modeling Approach
**Design Methodology (40 seconds)**
"Our UML modeling approach combines structural modeling through class diagrams, component architecture, and database schema design with behavioral modeling including sequence diagrams, plugin lifecycle management, and user workflow documentation. This systematic progression ensures requirements translate effectively to implementation."

## Slide 29: High-Level Architecture
**System Overview (25 seconds)**
"This diagram illustrates the complete PENQUEST platform architecture, showing how all components integrate to provide a seamless educational experience."

## Slide 30: Platform Overview
**Component Relationships (25 seconds)**
"The platform overview demonstrates the relationships between CTFd core, containerized challenges, web desktop environments, and monitoring systems."

## Slide 31: Web Desktop Class Diagram
**Database Design (30 seconds)**
"The web desktop component class structure shows our domain-driven design approach, with comprehensive database relationships supporting educational assessment and framework integration."

## Slide 32: Security Monitoring Flow
**Event Collection (30 seconds)**
"Security event collection integrates seamlessly with the request lifecycle, providing transparent monitoring without impacting user experience or system performance."

## Slide 33: Challenge Tracking Database
**Educational Schema (30 seconds)**
"Our challenge tracking database implements domain-driven design principles, with comprehensive framework integration supporting OWASP, MITRE, CWE, and CVSS classification systems."

## Slide 34: Web Desktop Sequence Diagram
**User Workflow (35 seconds)**
"This sequence diagram illustrates the complete user interaction workflow, from initial desktop request through container provisioning to final environment access."

## Slide 35: Security Monitoring Architecture
**Transparent Protection (35 seconds)**
"Our transparent shield methodology provides comprehensive platform monitoring without interfering with the educational experience, collecting security events for analysis and threat detection."

## Slide 36: Challenge Tracking System
**Modern Architecture (35 seconds)**
"The React plus FastAPI architecture provides modern, high-performance educational assessment capabilities with seamless framework integration."

## Slide 37: Integration Patterns
**Unified Ecosystem (45 seconds)**
"Our integration patterns create a unified educational ecosystem where CTFd core, CTFd-Whale, Web Desktop, and Security Monitor plugins work together through separation of concerns, educational focus, and extensible framework design. This demonstrates how technical sophistication serves educational objectives."

## Slide 38: Section 4 Introduction
**Implementation Opening (15 seconds)**
"Section 4 details the implementation and technical realization of the PENQUEST platform."

## Slide 39: Infrastructure Setup
**Foundation Implementation (40 seconds)**
"Infrastructure setup begins with Docker Swarm initialization and node labeling for targeted deployment. Overlay network creation establishes the FRP communication network and container isolation network, using carefully planned IP ranges to ensure security and performance."

## Slide 40: FRP Configuration
**Service Orchestration (35 seconds)**
"FRP server configuration enables secure container access tunneling using token-based authentication and virtual host management. Docker Compose orchestrates core platform services including CTFd, MariaDB, and Redis with appropriate resource limits and security constraints."

## Slide 41: Web Desktop Plugin
**Plugin Development (45 seconds)**
"Web desktop plugin implementation demonstrates Flask plugin architecture through the load() function, registering assets, creating database tables, and blueprint registration. Container management integrates with whale's infrastructure, providing seamless desktop environment provisioning for cybersecurity tool access."

## Slide 42: Security Monitoring Plugin
**Real-time Protection (50 seconds)**
"Security monitoring implementation uses Flask before_request hooks for transparent request analysis, skipping static resources for performance. Real-time threat detection employs sliding window rate limiting with automatic cleanup of expired entries and configurable threshold-based alerting."

## Slide 43: Challenge Tracking System
**Modern API Development (45 seconds)**
"The FastAPI backend provides automatic validation, comprehensive filtering, and seamless database integration. Security framework integration maps challenges to OWASP categories, CWE identifiers, MITRE ATT&CK techniques, and CVSS scores, creating a comprehensive educational metadata system."

## Slide 44: Security Hardening
**Production Readiness (45 seconds)**
"Security hardening includes container security configuration with no-new-privileges, capability dropping, and resource limits. System capacity validation confirms our mathematical models, with examples ranging from 7 users on 8GB systems to 95 users on 64GB systems, ensuring scalable educational deployment."

## Slide 45: Section 5 Introduction
**Results Opening (15 seconds)**
"Section 5 presents our results and validation analysis, demonstrating the successful delivery of a complete cybersecurity education platform."

## Slide 46: Deliverables Overview
**Achievement Summary (40 seconds)**
"PENQUEST delivers a comprehensive challenge portfolio of 7 challenges covering CVSS scores from 6.1 to 9.8, spanning medium to critical severity levels. OWASP coverage includes 5 of 10 categories, focusing on the most critical web application vulnerabilities. The complete platform stack integrates CTFd, Whale, Web Desktop, and Security Monitor into a unified educational environment."

## Slide 47: Platform Demo
**Live Demonstration (30 seconds)**
"The video demonstration showcases the complete user journey from registration through challenge completion, highlighting the seamless integration between theoretical concepts and practical implementation that makes PENQUEST unique in cybersecurity education."

## Slide 48: Performance Validation
**Scalability Confirmation (45 seconds)**
"Performance validation confirms our capacity models with validated user counts: 7 users on 8GB systems for small labs, 20 users on 16GB for standard classrooms, 45 users on 32GB for large workshops, and 95 users on 64GB for competition-scale events. Resource optimization achievements demonstrate efficient container utilization and system stability."

## Slide 49: Educational Impact Assessment
**Learning Outcomes (40 seconds)**
"Educational impact assessment shows strong challenge completion rates, high user engagement metrics, and effective learning progression tracking. Students demonstrate improved practical skills and better understanding of security frameworks through hands-on experience with real vulnerabilities."

## Slide 50: Technical Achievements
**System Performance (35 seconds)**
"Technical achievements include excellent platform stability metrics, effective security monitoring with zero false positives, and system performance benchmarks exceeding industry standards for educational platforms."

## Slide 51: Section 6 Introduction
**Conclusion Opening (15 seconds)**
"Section 6 concludes with project summary, contributions, limitations, and future perspectives."

## Slide 52: Project Summary
**Major Achievements (40 seconds)**
"PENQUEST successfully bridges the gap between theoretical cybersecurity education and practical industry needs. We've delivered a complete platform that makes advanced cybersecurity training accessible, engaging, and effective through innovative containerization and framework integration."

## Slide 53: Contributions
**Innovation Highlights (45 seconds)**
"Our contributions include significant technical advances in CTF platform development, educational methodology improvements through systematic framework integration, and innovative approaches to security monitoring in educational environments. These contributions have potential for broad adoption in cybersecurity education globally."

## Slide 54: Limitations & Challenges
**Honest Assessment (40 seconds)**
"We acknowledge certain limitations including resource constraints that limit concurrent user capacity, scalability considerations for very large deployments, and technical challenges in balancing security with educational accessibility. These provide clear directions for future development."

## Slide 55: Future Perspectives
**Vision Forward (45 seconds)**
"Future perspectives include platform expansion with additional security domains, advanced features like AI-powered hint systems and automated assessment, and broader educational integration with university curricula and professional certification programs. The foundation we've built supports unlimited educational innovation."

## Slide 56: Questions & Discussion
**Closing (30 seconds)**
"Thank you for your attention. I'm now ready to answer your questions and engage in detailed discussion about any aspect of the PENQUEST platform. I want to express my gratitude to my supervisors from both ESI and DATAPROTECT, and to acknowledge the excellent collaboration that made this project possible."

## General Presentation Guidelines:
- **Timing:** Approximately 20-25 minutes total presentation
- **Tone:** Professional, confident, technically precise
- **Eye Contact:** Engage with all committee members
- **Gestures:** Use hands to emphasize key points
- **Voice:** Clear projection, varied pace
- **Questions:** Pause after each section for clarification
- **Technical Depth:** Be prepared for deep technical questions
- **Passion:** Show enthusiasm for cybersecurity education innovation
